<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Run Tests" type="GradleRunConfiguration" factoryName="Gradle" focusToolWindowBeforeRun="true">
    <ExternalSystemSettings>
      <option name="executionName" />
      <option name="externalProjectPath" value="$PROJECT_DIR$/extensions/intellij" />
      <option name="externalSystemIdString" value="GRADLE" />
      <option name="scriptParameters" value="" />
      <option name="taskDescriptions">
        <list />
      </option>
      <option name="taskNames">
        <list>
          <option value="test" />
        </list>
      </option>
       <option name="vmOptions" value="-Dide.browser.jcef.out-of-process.enabled=false"/>
    </ExternalSystemSettings>
    <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
    <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
    <DebugAllEnabled>false</DebugAllEnabled>
    <RunAsTest>false</RunAsTest>
    <method v="2" />
  </configuration>
</component>