{
  "compilerOptions": {
    "module": "commonjs",
    "target": "ES2022",
    "lib": ["ES2022", "dom", "es6", "es5", "dom.iterable", "scripthost"],
    "outDir": "out/tsc/src",
    "sourceMap": true,
    "rootDir": "../../",
    "strict": true,
    "esModuleInterop": true,
    "resolveJsonModule": true,
    "skipLibCheck": true,
    "types": ["mocha"] // This solves conflict between jest and mocha: https://github.com/cypress-io/cypress/issues/7435#issuecomment-631695007
  },
  "include": ["src/**/*", "../../core/**/*.ts", "../../core/**/*.d.ts"],
  // core/test uses @types/jest, which conflicts with @types/mocha
  "exclude": [
    "../../core/dist/**/*",
    "../../core/**/*.test.ts",
    "../../core/**/test/**/*",
    "../../core/**/*.skip.ts"
  ]
}
