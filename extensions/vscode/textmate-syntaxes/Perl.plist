<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>comment</key>
	<string>
	TODO:	Include RegExp syntax
</string>
	<key>fileTypes</key>
	<array>
		<string>pl</string>
		<string>pm</string>
		<string>pod</string>
		<string>t</string>
		<string>PL</string>
		<string>psgi</string>
		<string>vcl</string>
	</array>
	<key>firstLineMatch</key>
	<string>^#!.*\bperl\b</string>
	<key>keyEquivalent</key>
	<string>^~P</string>
	<key>name</key>
	<string>Perl</string>
	<key>patterns</key>
	<array>
		<dict>
			<key>include</key>
			<string>#line_comment</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>^=</string>
			<key>captures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.comment.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>^=cut</string>
			<key>name</key>
			<string>comment.block.documentation.perl</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#variable</string>
		</dict>
		<dict>
			<key>applyEndPatternLast</key>
			<integer>1</integer>
			<key>begin</key>
			<string>\b(?=qr\s*[^\s\w])</string>
			<key>comment</key>
			<string>string.regexp.compile.perl</string>
			<key>end</key>
			<string>((([egimosxradlupc]*)))(?=(\s+\S|\s*[;\,\#\{\}\)]|\s*$))</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>string.regexp.compile.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>keyword.control.regexp-option.perl</string>
				</dict>
			</dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(qr)\s*\{</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.perl</string>
						</dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>support.function.perl</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\}</string>
					<key>name</key>
					<string>string.regexp.compile.nested_braces.perl</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#variable</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#nested_braces_interpolated</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(qr)\s*\[</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.perl</string>
						</dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>support.function.perl</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\]</string>
					<key>name</key>
					<string>string.regexp.compile.nested_brackets.perl</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#variable</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#nested_brackets_interpolated</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(qr)\s*&lt;</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.perl</string>
						</dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>support.function.perl</string>
						</dict>
					</dict>
					<key>end</key>
					<string>&gt;</string>
					<key>name</key>
					<string>string.regexp.compile.nested_ltgt.perl</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#variable</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#nested_ltgt_interpolated</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(qr)\s*\(</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.perl</string>
						</dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>support.function.perl</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\)</string>
					<key>name</key>
					<string>string.regexp.compile.nested_parens.perl</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>comment</key>
							<string>This is to prevent thinks like qr/foo$/ to treat $/ as a variable</string>
							<key>match</key>
							<string>\$(?=[^\s\w\\'\{\[\(\&lt;])</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#variable</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#nested_parens_interpolated</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(qr)\s*'</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.perl</string>
						</dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>support.function.perl</string>
						</dict>
					</dict>
					<key>end</key>
					<string>'</string>
					<key>name</key>
					<string>string.regexp.compile.single-quote.perl</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(qr)\s*([^\s\w'\{\[\(\&lt;])</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.perl</string>
						</dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>support.function.perl</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\2</string>
					<key>name</key>
					<string>string.regexp.compile.simple-delimiter.perl</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>comment</key>
							<string>This is to prevent thinks like qr/foo$/ to treat $/ as a variable</string>
							<key>match</key>
							<string>\$(?=[^\s\w'\{\[\(\&lt;])</string>
							<key>name</key>
							<string>keyword.control.anchor.perl</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#variable</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#nested_parens_interpolated</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<dict>
			<key>applyEndPatternLast</key>
			<integer>1</integer>
			<key>begin</key>
			<string>(?&lt;!\{|\+|\-)\b(?=m\s*[^\sa-zA-Z0-9])</string>
			<key>comment</key>
			<string>string.regexp.find-m.perl</string>
			<key>end</key>
			<string>((([egimosxradlupc]*)))(?=(\s+\S|\s*[;\,\#\{\}\)]|\s*$))</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>string.regexp.find-m.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>keyword.control.regexp-option.perl</string>
				</dict>
			</dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(m)\s*\{</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.perl</string>
						</dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>support.function.perl</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\}</string>
					<key>name</key>
					<string>string.regexp.find-m.nested_braces.perl</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#variable</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#nested_braces_interpolated</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(m)\s*\[</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.perl</string>
						</dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>support.function.perl</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\]</string>
					<key>name</key>
					<string>string.regexp.find-m.nested_brackets.perl</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#variable</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#nested_brackets_interpolated</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(m)\s*&lt;</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.perl</string>
						</dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>support.function.perl</string>
						</dict>
					</dict>
					<key>end</key>
					<string>&gt;</string>
					<key>name</key>
					<string>string.regexp.find-m.nested_ltgt.perl</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#variable</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#nested_ltgt_interpolated</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(m)\s*\(</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.perl</string>
						</dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>support.function.perl</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\)</string>
					<key>name</key>
					<string>string.regexp.find-m.nested_parens.perl</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#variable</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#nested_parens_interpolated</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(m)\s*'</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.perl</string>
						</dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>support.function.perl</string>
						</dict>
					</dict>
					<key>end</key>
					<string>'</string>
					<key>name</key>
					<string>string.regexp.find-m.single-quote.perl</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(?&lt;!\{|\+|\-)(m)\s*([^\sa-zA-Z0-9'\{\[\(\&lt;])</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.perl</string>
						</dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>support.function.perl</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\2</string>
					<key>name</key>
					<string>string.regexp.find-m.simple-delimiter.perl</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>comment</key>
							<string>This is to prevent thinks like qr/foo$/ to treat $/ as a variable</string>
							<key>match</key>
							<string>\$(?=[^\sa-zA-Z0-9'\{\[\(\&lt;])</string>
							<key>name</key>
							<string>keyword.control.anchor.perl</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#variable</string>
						</dict>
						<dict>
							<key>begin</key>
							<string>\[</string>
							<key>beginCaptures</key>
							<dict>
								<key>1</key>
								<dict>
									<key>name</key>
									<string>punctuation.definition.character-class.begin.perl</string>
								</dict>
							</dict>
							<key>end</key>
							<string>\]</string>
							<key>endCaptures</key>
							<dict>
								<key>1</key>
								<dict>
									<key>name</key>
									<string>punctuation.definition.character-class.end.perl</string>
								</dict>
							</dict>
							<key>name</key>
							<string>constant.other.character-class.set.perl</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>comment</key>
									<string>This is to prevent thinks like qr/foo$/ to treat $/ as a variable</string>
									<key>match</key>
									<string>\$(?=[^\s\w'\{\[\(\&lt;])</string>
									<key>name</key>
									<string>keyword.control.anchor.perl</string>
								</dict>
								<dict>
									<key>include</key>
									<string>#escaped_char</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>include</key>
							<string>#nested_parens_interpolated</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<dict>
			<key>applyEndPatternLast</key>
			<integer>1</integer>
			<key>begin</key>
			<string>\b(?=(?&lt;!\&amp;)(s)(\s+\S|\s*[;\,\#\{\}\(\)\[&lt;]|$))</string>
			<key>comment</key>
			<string>string.regexp.replace.perl</string>
			<key>end</key>
			<string>((([egimosxradlupc]*)))(?=(\s+\S|\s*[;\,\#\{\}\)\]&gt;]|\s*$))</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>string.regexp.replace.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>keyword.control.regexp-option.perl</string>
				</dict>
			</dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(s)\s*\{</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.perl</string>
						</dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>support.function.perl</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\}</string>
					<key>name</key>
					<string>string.regexp.nested_braces.perl</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#nested_braces</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(s)\s*\[</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.perl</string>
						</dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>support.function.perl</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\]</string>
					<key>name</key>
					<string>string.regexp.nested_brackets.perl</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#nested_brackets</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(s)\s*&lt;</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.perl</string>
						</dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>support.function.perl</string>
						</dict>
					</dict>
					<key>end</key>
					<string>&gt;</string>
					<key>name</key>
					<string>string.regexp.nested_ltgt.perl</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#nested_ltgt</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(s)\s*\(</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.perl</string>
						</dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>support.function.perl</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\)</string>
					<key>name</key>
					<string>string.regexp.nested_parens.perl</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#nested_parens</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>\{</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.perl</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\}</string>
					<key>name</key>
					<string>string.regexp.format.nested_braces.perl</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#variable</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#nested_braces_interpolated</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>\[</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.perl</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\]</string>
					<key>name</key>
					<string>string.regexp.format.nested_brackets.perl</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#variable</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#nested_brackets_interpolated</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>&lt;</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.perl</string>
						</dict>
					</dict>
					<key>end</key>
					<string>&gt;</string>
					<key>name</key>
					<string>string.regexp.format.nested_ltgt.perl</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#variable</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#nested_ltgt_interpolated</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>\(</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.perl</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\)</string>
					<key>name</key>
					<string>string.regexp.format.nested_parens.perl</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#variable</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#nested_parens_interpolated</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>'</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.perl</string>
						</dict>
					</dict>
					<key>end</key>
					<string>'</string>
					<key>name</key>
					<string>string.regexp.format.single_quote.perl</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>match</key>
							<string>\\['\\]</string>
							<key>name</key>
							<string>constant.character.escape.perl</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>([^\s\w\[({&lt;;])</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.perl</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\1</string>
					<key>name</key>
					<string>string.regexp.format.simple_delimiter.perl</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#variable</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>match</key>
					<string>\s+</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>\b(?=s([^\sa-zA-Z0-9\[({&lt;]).*\1([egimosxradlupc]*)([\}\)\;\,]|\s+))</string>
			<key>comment</key>
			<string>string.regexp.replaceXXX</string>
			<key>end</key>
			<string>((([egimosxradlupc]*)))(?=([\}\)\;\,]|\s+|$))</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>string.regexp.replace.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>keyword.control.regexp-option.perl</string>
				</dict>
			</dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(s\s*)([^\sa-zA-Z0-9\[({&lt;])</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.perl</string>
						</dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>support.function.perl</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?=\2)</string>
					<key>name</key>
					<string>string.regexp.replaceXXX.simple_delimiter.perl</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>'</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.perl</string>
						</dict>
					</dict>
					<key>end</key>
					<string>'</string>
					<key>name</key>
					<string>string.regexp.replaceXXX.format.single_quote.perl</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>match</key>
							<string>\\['\\]</string>
							<key>name</key>
							<string>constant.character.escape.perl.perl</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>([^\sa-zA-Z0-9\[({&lt;])</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.perl</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\1</string>
					<key>name</key>
					<string>string.regexp.replaceXXX.format.simple_delimiter.perl</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#variable</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>\b(?=(?&lt;!\\)s\s*([^\s\w\[({&lt;&gt;]))</string>
			<key>comment</key>
			<string>string.regexp.replace.extended</string>
			<key>end</key>
			<string>((([egimosradlupc]*x[egimosradlupc]*)))\b</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>string.regexp.replace.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>keyword.control.regexp-option.perl</string>
				</dict>
			</dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(s)\s*(.)</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.perl</string>
						</dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>support.function.perl</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?=\2)</string>
					<key>name</key>
					<string>string.regexp.replace.extended.simple_delimiter.perl</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>'</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.perl</string>
						</dict>
					</dict>
					<key>end</key>
					<string>'(?=[egimosradlupc]*x[egimosradlupc]*)\b</string>
					<key>name</key>
					<string>string.regexp.replace.extended.simple_delimiter.perl</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(.)</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.perl</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\1(?=[egimosradlupc]*x[egimosradlupc]*)\b</string>
					<key>name</key>
					<string>string.regexp.replace.extended.simple_delimiter.perl</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#escaped_char</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#variable</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(?&lt;=\(|\{|~|&amp;)\s*((\/))</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>string.regexp.find.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
			</dict>
			<key>contentName</key>
			<string>string.regexp.find.perl</string>
			<key>end</key>
			<string>((\1([egimosxradlupc]*)))(?=(\s+\S|\s*[;\,\#\{\}\)]|\s*$))</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>string.regexp.find.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>keyword.control.regexp-option.perl</string>
				</dict>
			</dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>comment</key>
					<string>This is to prevent thinks like /foo$/ to treat $/ as a variable</string>
					<key>match</key>
					<string>\$(?=\/)</string>
					<key>name</key>
					<string>keyword.control.anchor.perl</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>constant.other.key.perl</string>
				</dict>
			</dict>
			<key>match</key>
			<string>\b(\w+)\s*(?==&gt;)</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?&lt;={)\s*\w+\s*(?=})</string>
			<key>name</key>
			<string>constant.other.bareword.perl</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>keyword.control.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>entity.name.type.class.perl</string>
				</dict>
			</dict>
			<key>match</key>
			<string>^\s*(package)\s+([^\s;]+)</string>
			<key>name</key>
			<string>meta.class.perl</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>storage.type.sub.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>entity.name.function.perl</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>storage.type.method.perl</string>
				</dict>
			</dict>
			<key>match</key>
			<string>\b(sub)(?:\s+([-a-zA-Z0-9_]+))?\s*(?:\([\$\@\*;]*\))?[^\w\{]</string>
			<key>name</key>
			<string>meta.function.perl</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>entity.name.function.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.parameters.perl</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>variable.parameter.function.perl</string>
				</dict>
			</dict>
			<key>match</key>
			<string>^\s*(BEGIN|UNITCHECK|CHECK|INIT|END|DESTROY)\b</string>
			<key>name</key>
			<string>meta.function.perl</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>^(?=(\t| {4}))</string>
			<key>end</key>
			<string>(?=[^\t\s])</string>
			<key>name</key>
			<string>meta.leading-tabs</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>meta.odd-tab</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>meta.even-tab</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(\t| {4})(\t| {4})?</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>support.function.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
				<key>5</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
				<key>8</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
			</dict>
			<key>match</key>
			<string>\b(tr|y)\s*([^A-Za-z0-9\s])(.*?)(?&lt;!\\)(\\{2})*(\2)(.*?)(?&lt;!\\)(\\{2})*(\2)</string>
			<key>name</key>
			<string>string.regexp.replace.perl</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(__FILE__|__LINE__|__PACKAGE__|__SUB__)\b</string>
			<key>name</key>
			<string>constant.language.perl</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>\b(__DATA__|__END__)\n?</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>constant.language.perl</string>
				</dict>
			</dict>
			<key>contentName</key>
			<string>text.plain</string>
			<key>end</key>
			<string>\z</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?&lt;!-&gt;)\b(continue|die|do|else|elsif|exit|for|foreach|goto|if|last|next|redo|return|select|unless|until|wait|while|switch|case|require|use|eval)\b</string>
			<key>name</key>
			<string>keyword.control.perl</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(my|our|local)\b</string>
			<key>name</key>
			<string>storage.modifier.perl</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?&lt;!\w)\-[rwx0RWXOezsfdlpSbctugkTBMAC]\b</string>
			<key>name</key>
			<string>keyword.operator.filetest.perl</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(and|or|xor|as|not)\b</string>
			<key>name</key>
			<string>keyword.operator.logical.perl</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(&lt;=&gt;|=&gt;|-&gt;)</string>
			<key>name</key>
			<string>keyword.operator.comparison.perl</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>(((&lt;&lt;) *"HTML"))(.*)\n?</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>string.unquoted.heredoc.doublequote.perl</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.heredoc.perl</string>
				</dict>
				<key>4</key>
				<dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>$self</string>
						</dict>
					</array>
				</dict>
			</dict>
			<key>contentName</key>
			<string>text.html.embedded.perl</string>
			<key>end</key>
			<string>(^HTML$)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
				<dict>
					<key>include</key>
					<string>text.html.basic</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(((&lt;&lt;) *"XML"))(.*)\n?</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>string.unquoted.heredoc.doublequote.perl</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.heredoc.perl</string>
				</dict>
				<key>4</key>
				<dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>$self</string>
						</dict>
					</array>
				</dict>
			</dict>
			<key>contentName</key>
			<string>text.xml.embedded.perl</string>
			<key>end</key>
			<string>(^XML$)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
				<dict>
					<key>include</key>
					<string>text.xml</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(((&lt;&lt;) *"CSS"))(.*)\n?</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>string.unquoted.heredoc.doublequote.perl</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.heredoc.perl</string>
				</dict>
				<key>4</key>
				<dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>$self</string>
						</dict>
					</array>
				</dict>
			</dict>
			<key>contentName</key>
			<string>text.css.embedded.perl</string>
			<key>end</key>
			<string>(^CSS$)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
				<dict>
					<key>include</key>
					<string>source.css</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(((&lt;&lt;) *"JAVASCRIPT"))(.*)\n?</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>string.unquoted.heredoc.doublequote.perl</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.heredoc.perl</string>
				</dict>
				<key>4</key>
				<dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>$self</string>
						</dict>
					</array>
				</dict>
			</dict>
			<key>contentName</key>
			<string>text.js.embedded.perl</string>
			<key>end</key>
			<string>(^JAVASCRIPT$)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
				<dict>
					<key>include</key>
					<string>source.js</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(((&lt;&lt;) *"SQL"))(.*)\n?</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>string.unquoted.heredoc.doublequote.perl</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.heredoc.perl</string>
				</dict>
				<key>4</key>
				<dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>$self</string>
						</dict>
					</array>
				</dict>
			</dict>
			<key>contentName</key>
			<string>source.sql.embedded.perl</string>
			<key>end</key>
			<string>(^SQL$)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
				<dict>
					<key>include</key>
					<string>source.sql</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(((&lt;&lt;) *"POSTSCRIPT"))(.*)\n?</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>string.unquoted.heredoc.doublequote.perl</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.heredoc.perl</string>
				</dict>
				<key>4</key>
				<dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>$self</string>
						</dict>
					</array>
				</dict>
			</dict>
			<key>contentName</key>
			<string>text.postscript.embedded.perl</string>
			<key>end</key>
			<string>(^POSTSCRIPT$)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
				<dict>
					<key>include</key>
					<string>source.postscript</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(((&lt;&lt;) *"([^"]*)"))(.*)\n?</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>string.unquoted.heredoc.doublequote.perl</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.heredoc.perl</string>
				</dict>
				<key>4</key>
				<dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>$self</string>
						</dict>
					</array>
				</dict>
			</dict>
			<key>contentName</key>
			<string>string.unquoted.heredoc.doublequote.perl</string>
			<key>end</key>
			<string>(^\4$)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(((&lt;&lt;) *'HTML'))(.*)\n?</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>string.unquoted.heredoc.quote.perl</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.heredoc.perl</string>
				</dict>
				<key>4</key>
				<dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>$self</string>
						</dict>
					</array>
				</dict>
			</dict>
			<key>contentName</key>
			<string>text.html.embedded.perl</string>
			<key>end</key>
			<string>(^HTML$)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>text.html.basic</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(((&lt;&lt;) *'XML'))(.*)\n?</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>string.unquoted.heredoc.quote.perl</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.heredoc.perl</string>
				</dict>
				<key>4</key>
				<dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>$self</string>
						</dict>
					</array>
				</dict>
			</dict>
			<key>contentName</key>
			<string>text.xml.embedded.perl</string>
			<key>end</key>
			<string>(^XML$)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>text.xml</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(((&lt;&lt;) *'CSS'))(.*)\n?</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>string.unquoted.heredoc.quote.perl</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.heredoc.perl</string>
				</dict>
				<key>4</key>
				<dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>$self</string>
						</dict>
					</array>
				</dict>
			</dict>
			<key>contentName</key>
			<string>text.css.embedded.perl</string>
			<key>end</key>
			<string>(^CSS$)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>source.css</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(((&lt;&lt;) *'JAVASCRIPT'))(.*)\n?</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>string.unquoted.heredoc.quote.perl</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.heredoc.perl</string>
				</dict>
				<key>4</key>
				<dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>$self</string>
						</dict>
					</array>
				</dict>
			</dict>
			<key>contentName</key>
			<string>text.js.embedded.perl</string>
			<key>end</key>
			<string>(^JAVASCRIPT$)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>source.js</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(((&lt;&lt;) *'SQL'))(.*)\n?</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>string.unquoted.heredoc.quote.perl</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.heredoc.perl</string>
				</dict>
				<key>4</key>
				<dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>$self</string>
						</dict>
					</array>
				</dict>
			</dict>
			<key>contentName</key>
			<string>source.sql.embedded.perl</string>
			<key>end</key>
			<string>(^SQL$)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>source.sql</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(((&lt;&lt;) *'POSTSCRIPT'))(.*)\n?</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>string.unquoted.heredoc.quote.perl</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.heredoc.perl</string>
				</dict>
				<key>4</key>
				<dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>$self</string>
						</dict>
					</array>
				</dict>
			</dict>
			<key>contentName</key>
			<string>source.postscript.embedded.perl</string>
			<key>end</key>
			<string>(^POSTSCRIPT$)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>source.postscript</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(((&lt;&lt;) *'([^']*)'))(.*)\n?</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>string.unquoted.heredoc.quote.perl</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.heredoc.perl</string>
				</dict>
				<key>4</key>
				<dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>$self</string>
						</dict>
					</array>
				</dict>
			</dict>
			<key>contentName</key>
			<string>string.unquoted.heredoc.quote.perl</string>
			<key>end</key>
			<string>(^\4$)</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>(((&lt;&lt;) *\\((?![=\d\$\( ])[^;,'"`\s\)]*)))(.*)\n?</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>string.unquoted.heredoc.quote.perl</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.heredoc.perl</string>
				</dict>
				<key>4</key>
				<dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>$self</string>
						</dict>
					</array>
				</dict>
			</dict>
			<key>contentName</key>
			<string>string.unquoted.heredoc.quote.perl</string>
			<key>end</key>
			<string>(^\4$)</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>(((&lt;&lt;) *`([^`]*)`))(.*)\n?</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>string.unquoted.heredoc.backtick.perl</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.heredoc.perl</string>
				</dict>
				<key>4</key>
				<dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>$self</string>
						</dict>
					</array>
				</dict>
			</dict>
			<key>contentName</key>
			<string>string.unquoted.heredoc.backtick.perl</string>
			<key>end</key>
			<string>(^\4$)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(((&lt;&lt;) *HTML\b))(.*)\n?</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>string.unquoted.heredoc.perl</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.heredoc.perl</string>
				</dict>
				<key>4</key>
				<dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>$self</string>
						</dict>
					</array>
				</dict>
			</dict>
			<key>contentName</key>
			<string>text.html.embedded.perl</string>
			<key>end</key>
			<string>(^HTML$)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
				<dict>
					<key>include</key>
					<string>text.html.basic</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(((&lt;&lt;) *XML\b))(.*)\n?</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>string.unquoted.heredoc.perl</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.heredoc.perl</string>
				</dict>
				<key>4</key>
				<dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>$self</string>
						</dict>
					</array>
				</dict>
			</dict>
			<key>contentName</key>
			<string>text.xml.embedded.perl</string>
			<key>end</key>
			<string>(^XML$)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
				<dict>
					<key>include</key>
					<string>text.xml</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(((&lt;&lt;) *JAVASCRIPT\b))(.*)\n?</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>string.unquoted.heredoc.perl</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.heredoc.perl</string>
				</dict>
				<key>4</key>
				<dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>$self</string>
						</dict>
					</array>
				</dict>
			</dict>
			<key>contentName</key>
			<string>source.js.embedded.perl</string>
			<key>end</key>
			<string>(^JAVASCRIPT$)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
				<dict>
					<key>include</key>
					<string>source.js</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(((&lt;&lt;) *SQL\b))(.*)\n?</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>string.unquoted.heredoc.perl</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.heredoc.perl</string>
				</dict>
				<key>4</key>
				<dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>$self</string>
						</dict>
					</array>
				</dict>
			</dict>
			<key>contentName</key>
			<string>source.sql.embedded.perl</string>
			<key>end</key>
			<string>(^SQL$)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
				<dict>
					<key>include</key>
					<string>source.sql</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(((&lt;&lt;) *POSTSCRIPT\b))(.*)\n?</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>string.unquoted.heredoc.perl</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.heredoc.perl</string>
				</dict>
				<key>4</key>
				<dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>$self</string>
						</dict>
					</array>
				</dict>
			</dict>
			<key>contentName</key>
			<string>source.postscript.embedded.perl</string>
			<key>end</key>
			<string>(^POSTSCRIPT$)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
				<dict>
					<key>include</key>
					<string>source.postscript</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(((&lt;&lt;) *((?![=\d\$\( ])[^;,'"`\s\)]*)))(.*)\n?</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>string.unquoted.heredoc.perl</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.heredoc.perl</string>
				</dict>
				<key>5</key>
				<dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>$self</string>
						</dict>
					</array>
				</dict>
			</dict>
			<key>contentName</key>
			<string>string.unquoted.heredoc.perl</string>
			<key>end</key>
			<string>(^\4$)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>\bqq\s*([^\(\{\[\&lt;\w\s])</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\1</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.perl</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.other.qq.perl</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>\bqx\s*([^'\(\{\[\&lt;\w\s])</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\1</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.perl</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.interpolated.qx.perl</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>\bqx\s*'</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>'</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.perl</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.interpolated.qx.single-quote.perl</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>"</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>"</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.perl</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.double.perl</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(?&lt;!-&gt;)\bqw?\s*([^\(\{\[\&lt;\w\s])</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\1</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.perl</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.other.q.perl</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>'</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>'</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.perl</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.single.perl</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\\['\\]</string>
					<key>name</key>
					<string>constant.character.escape.perl</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>`</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>`</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.perl</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.interpolated.perl</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(?&lt;!-&gt;)\bqq\s*\(</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\)</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.perl</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.other.qq-paren.perl</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#nested_parens_interpolated</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>\bqq\s*\{</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\}</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.perl</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.other.qq-brace.perl</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#nested_braces_interpolated</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>\bqq\s*\[</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\]</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.perl</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.other.qq-bracket.perl</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#nested_brackets_interpolated</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>\bqq\s*\&lt;</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\&gt;</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.perl</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.other.qq-ltgt.perl</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#nested_ltgt_interpolated</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(?&lt;!-&gt;)\bqx\s*\(</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\)</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.perl</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.interpolated.qx-paren.perl</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#nested_parens_interpolated</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>\bqx\s*\{</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\}</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.perl</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.interpolated.qx-brace.perl</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#nested_braces_interpolated</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>\bqx\s*\[</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\]</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.perl</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.interpolated.qx-bracket.perl</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#nested_brackets_interpolated</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>\bqx\s*\&lt;</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\&gt;</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.perl</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.interpolated.qx-ltgt.perl</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#nested_ltgt_interpolated</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(?&lt;!-&gt;)\bqw?\s*\(</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\)</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.perl</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.other.q-paren.perl</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#nested_parens</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>\bqw?\s*\{</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\}</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.perl</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.other.q-brace.perl</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#nested_braces</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>\bqw?\s*\[</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\]</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.perl</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.other.q-bracket.perl</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#nested_brackets</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>\bqw?\s*\&lt;</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\&gt;</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.perl</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.other.q-ltgt.perl</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#nested_ltgt</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>^__\w+__</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>$</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.perl</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.unquoted.program-block.perl</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>\b(format)\s+(\w+)\s*=</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>support.function.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>entity.name.function.format.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>^\.\s*$</string>
			<key>name</key>
			<string>meta.format.perl</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#line_comment</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>support.function.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>entity.name.function.perl</string>
				</dict>
			</dict>
			<key>match</key>
			<string>\b(x)\s*(\d+)\b</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(ARGV|DATA|ENV|SIG|STDERR|STDIN|STDOUT|atan2|bind|binmode|bless|caller|chdir|chmod|chomp|chop|chown|chr|chroot|close|closedir|cmp|connect|cos|crypt|dbmclose|dbmopen|defined|delete|dump|each|endgrent|endhostent|endnetent|endprotoent|endpwent|endservent|eof|eq|eval|exec|exists|exp|fcntl|fileno|flock|fork|formline|ge|getc|getgrent|getgrgid|getgrnam|gethostbyaddr|gethostbyname|gethostent|getlogin|getnetbyaddr|getnetbyname|getnetent|getpeername|getpgrp|getppid|getpriority|getprotobyname|getprotobynumber|getprotoent|getpwent|getpwnam|getpwuid|getservbyname|getservbyport|getservent|getsockname|getsockopt|glob|gmtime|grep|gt|hex|import|index|int|ioctl|join|keys|kill|lc|lcfirst|le|length|link|listen|local|localtime|log|lstat|lt|m|map|mkdir|msgctl|msgget|msgrcv|msgsnd|ne|no|oct|open|opendir|ord|pack|pipe|pop|pos|print|printf|push|quotemeta|rand|read|readdir|readlink|recv|ref|rename|reset|reverse|rewinddir|rindex|rmdir|s|scalar|seek|seekdir|semctl|semget|semop|send|setgrent|sethostent|setnetent|setpgrp|setpriority|setprotoent|setpwent|setservent|setsockopt|shift|shmctl|shmget|shmread|shmwrite|shutdown|sin|sleep|socket|socketpair|sort|splice|split|sprintf|sqrt|srand|stat|study|substr|symlink|syscall|sysopen|sysread|system|syswrite|tell|telldir|tie|tied|time|times|tr|truncate|uc|ucfirst|umask|undef|unlink|unpack|unshift|untie|utime|values|vec|waitpid|wantarray|warn|write|y)\b</string>
			<key>name</key>
			<string>support.function.perl</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.scope.begin.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.scope.end.perl</string>
				</dict>
			</dict>
			<key>comment</key>
			<string>Match empty brackets for ↩ snippet</string>
			<key>match</key>
			<string>(\{)(\})</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.scope.begin.perl</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.scope.end.perl</string>
				</dict>
			</dict>
			<key>comment</key>
			<string>Match empty parenthesis for ↩ snippet</string>
			<key>match</key>
			<string>(\()(\))</string>
		</dict>
	</array>
	<key>repository</key>
	<dict>
		<key>escaped_char</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\\\d+</string>
					<key>name</key>
					<string>constant.character.escape.perl</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\\c[^\s\\]</string>
					<key>name</key>
					<string>constant.character.escape.perl</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\\g(?:\{(?:\w*|-\d+)\}|\d+)</string>
					<key>name</key>
					<string>constant.character.escape.perl</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\\k(?:\{\w*\}|&lt;\w*&gt;|'\w*')</string>
					<key>name</key>
					<string>constant.character.escape.perl</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\\N\{[^\}]*\}</string>
					<key>name</key>
					<string>constant.character.escape.perl</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\\o\{\d*\}</string>
					<key>name</key>
					<string>constant.character.escape.perl</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\\(?:p|P)(?:\{\w*\}|P)</string>
					<key>name</key>
					<string>constant.character.escape.perl</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\\x(?:[0-9a-zA-Z]{2}|\{\w*\})?</string>
					<key>name</key>
					<string>constant.character.escape.perl</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\\.</string>
					<key>name</key>
					<string>constant.character.escape.perl</string>
				</dict>
			</array>
		</dict>
		<key>line_comment</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(^[ \t]+)?(?=#)</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.whitespace.comment.leading.perl</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?!\G)</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>#</string>
							<key>beginCaptures</key>
							<dict>
								<key>0</key>
								<dict>
									<key>name</key>
									<string>punctuation.definition.comment.perl</string>
								</dict>
							</dict>
							<key>end</key>
							<string>\n</string>
							<key>name</key>
							<string>comment.line.number-sign.perl</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<key>nested_braces</key>
		<dict>
			<key>begin</key>
			<string>\{</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.scope.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\}</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#nested_braces</string>
				</dict>
			</array>
		</dict>
		<key>nested_braces_interpolated</key>
		<dict>
			<key>begin</key>
			<string>\{</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.scope.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\}</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#nested_braces_interpolated</string>
				</dict>
			</array>
		</dict>
		<key>nested_brackets</key>
		<dict>
			<key>begin</key>
			<string>\[</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.scope.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\]</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#nested_brackets</string>
				</dict>
			</array>
		</dict>
		<key>nested_brackets_interpolated</key>
		<dict>
			<key>begin</key>
			<string>\[</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.scope.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\]</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#nested_brackets_interpolated</string>
				</dict>
			</array>
		</dict>
		<key>nested_ltgt</key>
		<dict>
			<key>begin</key>
			<string>&lt;</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.scope.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>&gt;</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#nested_ltgt</string>
				</dict>
			</array>
		</dict>
		<key>nested_ltgt_interpolated</key>
		<dict>
			<key>begin</key>
			<string>&lt;</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.scope.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>&gt;</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#nested_ltgt_interpolated</string>
				</dict>
			</array>
		</dict>
		<key>nested_parens</key>
		<dict>
			<key>begin</key>
			<string>\(</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.scope.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#nested_parens</string>
				</dict>
			</array>
		</dict>
		<key>nested_parens_interpolated</key>
		<dict>
			<key>begin</key>
			<string>\(</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.scope.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>comment</key>
					<string>This is to prevent thinks like qr/foo$/ to treat $/ as a variable</string>
					<key>match</key>
					<string>\$(?=[^\s\w'\{\[\(\&lt;])</string>
					<key>name</key>
					<string>keyword.control.anchor.perl</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#escaped_char</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#nested_parens_interpolated</string>
				</dict>
			</array>
		</dict>
		<key>variable</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.variable.perl</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(\$)&amp;(?![A-Za-z0-9_])</string>
					<key>name</key>
					<string>variable.other.regexp.match.perl</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.variable.perl</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(\$)`(?![A-Za-z0-9_])</string>
					<key>name</key>
					<string>variable.other.regexp.pre-match.perl</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.variable.perl</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(\$)'(?![A-Za-z0-9_])</string>
					<key>name</key>
					<string>variable.other.regexp.post-match.perl</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.variable.perl</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(\$)\+(?![A-Za-z0-9_])</string>
					<key>name</key>
					<string>variable.other.regexp.last-paren-match.perl</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.variable.perl</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(\$)"(?![A-Za-z0-9_])</string>
					<key>name</key>
					<string>variable.other.readwrite.list-separator.perl</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.variable.perl</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(\$)0(?![A-Za-z0-9_])</string>
					<key>name</key>
					<string>variable.other.predefined.program-name.perl</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.variable.perl</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(\$)[_ab\*\.\/\|,\\;#%=\-~^:?!\$&lt;&gt;\(\)\[\]@](?![A-Za-z0-9_])</string>
					<key>name</key>
					<string>variable.other.predefined.perl</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.variable.perl</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(\$)[0-9]+(?![A-Za-z0-9_])</string>
					<key>name</key>
					<string>variable.other.subpattern.perl</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.variable.perl</string>
						</dict>
					</dict>
					<key>match</key>
					<string>([\$\@\%](#)?)([a-zA-Zx7f-xff\$]|::)([a-zA-Z0-9_x7f-xff\$]|::)*\b</string>
					<key>name</key>
					<string>variable.other.readwrite.global.perl</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.variable.perl</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.variable.perl</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(\$\{)(?:[a-zA-Zx7f-xff\$]|::)(?:[a-zA-Z0-9_x7f-xff\$]|::)*(\})</string>
					<key>name</key>
					<string>variable.other.readwrite.global.perl</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.variable.perl</string>
						</dict>
					</dict>
					<key>match</key>
					<string>([\$\@\%](#)?)[0-9_]\b</string>
					<key>name</key>
					<string>variable.other.readwrite.global.special.perl</string>
				</dict>
			</array>
		</dict>
	</dict>
	<key>scopeName</key>
	<string>source.perl</string>
	<key>uuid</key>
	<string>EDBFE125-6B1C-11D9-9189-000D93589AF6</string>
</dict>
</plist>