# Welcome to the Continue Visual Studio Code Extension

## Get up and running straight away

- Open the root of the continue repository in visual studio code
- Press `F5` to open a new window with your extension loaded.
- The continue in the launched vs code instance uses `extensions/.continue-debug` as configuration folder
- Find output from your extension in the debug console.

## Make changes

- You can relaunch the extension from the debug toolbar after changing code.
- You can also reload (`Ctrl+R` or `Cmd+R` on Mac) the VS Code window with your extension to load your changes.
