---
title: MCP
description: MCP use and customization
keywords: [tool, use, function calling, claude, automatic]
---

import TabItem from "@theme/TabItem";
import Tabs from "@theme/Tabs";

## MCP

Currently custom tools can be configured using the [Model Context Protocol](https://modelcontextprotocol.io/introduction), a standard proposed by Anthropic to unify prompts, context, and tool use.

MCP Servers can be added to hub Assistants using `mcpServers` blocks. You can explore available MCP server blocks [here](https://hub.continue.dev/explore/mcp).

:::info
MCP can only be used in the **agent** mode.
:::

To set up your own MCP server, read the [MCP quickstart](https://modelcontextprotocol.io/quickstart) and then [create an `mcpServers` block](https://hub.continue.dev/new?type=block&blockType=mcpServers) or add the following to your [config file](./configuration.md):

<Tabs groupId="config-example">
  <TabItem value="yaml" label="YAML">
  ```yaml title="config.yaml"
  mcpServers:
    - name: My MCP Server
      command: uvx
      args:
        - mcp-server-sqlite
        - --db-path
        - /Users/<USER>/test.db
  ```
  </TabItem>
  <TabItem value="json" label="JSON">
  ```json title="config.json"
  {
    "experimental": {
      "modelContextProtocolServers": [
        {
          "transport": {
            "type": "stdio",
            "command": "uvx",
            "args": ["mcp-server-sqlite", "--db-path", "/Users/<USER>/test.db"]
          }  
        }
      ]
    }
  }
  ```
  </TabItem>
</Tabs>
