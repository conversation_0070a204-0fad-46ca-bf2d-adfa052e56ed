---
title: Anthropic
slug: ../anthropic
---

import Tab<PERSON><PERSON> from "@theme/TabItem";
import Tabs from "@theme/Tabs";

:::info
You can get an API key from the [Anthropic console](https://console.anthropic.com/account/keys).
:::

## Chat model

We recommend configuring **Claude 3.5 Sonnet** as your chat model.

<Tabs groupId="config-example">
  <TabItem value="yaml" label="YAML">
  ```yaml title="config.yaml"
  models:
    - name: Claude 3.5 Sonnet
      provider: anthropic
      model: claude-3-5-sonnet-latest
      apiKey: <YOUR_ANTHROPIC_API_KEY>
  ```
  </TabItem>
  <TabItem value="json" label="JSON">
  ```json title="config.json"
  {
    "models": [
      {
        "title": "Claude 3.5 Sonnet",
        "provider": "anthropic",
        "model": "claude-3-5-sonnet-latest",
        "apiKey": "<YOUR_ANTHROPIC_API_KEY>"
      }
    ]
  }
  ```
  </TabItem>
</Tabs>

## Autocomplete model

Anthropic currently does not offer any autocomplete models.

[Click here](../../model-roles/autocomplete.md) to see a list of autocomplete model providers.

## Embeddings model

Anthropic currently does not offer any embeddings models.

[Click here](../../model-roles/embeddings.mdx) to see a list of embeddings model providers.

## Reranking model

Anthropic currently does not offer any reranking models.

[Click here](../../model-roles/reranking.mdx) to see a list of reranking model providers.

## Prompt caching

Anthropic supports [prompt caching with Claude](https://docs.anthropic.com/en/docs/build-with-claude/prompt-caching).

To enable caching of the system message and the turn-by-turn conversation, update your your model configuration as following:

<Tabs groupId="config-example">
  <TabItem value="yaml" label="YAML">
  ```yaml title="config.yaml"
  models:
    - name: Anthropic
      provider: anthropic
      model: claude-3-5-sonnet-latest
      apiKey: <YOUR_ANTHROPIC_API_KEY>
      roles:
        - chat
      cacheBehavior:
        cacheSystemMessage: true
        cacheConversation: true
  ```
  </TabItem>
  <TabItem value="json" label="JSON">
  ```json title="config.json"
  {
    "models": [
      {
        "cacheBehavior": {
          "cacheSystemMessage": true,
          "cacheConversation": true
        },
        "title": "Anthropic",
        "provider": "anthropic",
        "model": "claude-3-5-sonnet-latest",
        "apiKey": "<YOUR_ANTHROPIC_API_KEY>"
      }
    ]
  }
  ```
  </TabItem>
</Tabs>
