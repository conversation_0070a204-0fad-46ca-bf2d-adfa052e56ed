import Tabs from "@theme/Tabs";
import TabItem from "@theme/TabItem";

# Relace

Relace provides a fast apply model through their API that helps you reliably and almost instantly apply chat suggestions to your codebase. You can sign up and obtain an API key [here](https://app.relace.ai/settings/api-keys). Then, change your configuration file to look like this:

<Tabs groupId="config-example">
   <TabItem value="yaml" label="YAML">
   ```yaml title="Package or config.yaml"
   models:
     - name: Relace Fast Apply
       provider: relace
       model: Fast-Apply
       apiKey: <YOUR_RELACE_API_KEY>
       roles:
         - apply
       promptTemplates:
         apply: "{{{ new_code }}}"
   ```
   </TabItem>
   <TabItem value="json" label="JSON">
	```json title="config.json"
	{
      "models": [
        {
          "title": "Relace Fast Apply",
          "provider": "relace",
          "model": "Fast-Apply",
          "apiKey": "<YOUR_RELACE_API_KEY>"
        }
      ]
	}
    ```
   </TabItem>
</Tabs>

[View the source](https://github.com/continuedev/continue/blob/main/core/llm/llms/Relace.ts)
