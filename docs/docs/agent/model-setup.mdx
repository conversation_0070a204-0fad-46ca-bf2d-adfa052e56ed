---
title: Model setup
description: Models for Continue Agent
keywords: [model, agent]
sidebar_position: 2
---

Agent can be used with models that support tool use through Continue.

For the best Agent experience we recommend Claude Sonnet 4 from Anthropic. Add Claude 3.7 Sonnet to your assistant from the hub [here](https://hub.continue.dev/anthropic/claude-4-sonnet). You can get an API key from the [Anthropic console](https://console.anthropic.com/settings/keys).

Currently the following models are recommended:

- [Anthropic](../customize/model-providers/top-level/anthropic.mdx) - we recommend [<PERSON> Sonnet 4](https://hub.continue.dev/anthropic/claude-4-sonnet)
- [OpenAI](../customize/model-providers/top-level/openai.mdx) - we recommend [GPT-4o](https://hub.continue.dev/openai/gpt-4o)
- [Gemini](../customize/model-providers/top-level/gemini.mdx) - we recommend [Gemini 2.5 Pro](https://hub.continue.dev/google/gemini-2.5-pro)

The best chat models generally support tool use. See [recommended chat models](../customize/model-roles/chat.mdx#recommended-chat-models) for more.
