---
title: Model setup
description: How to set up a Chat model
keywords: [model]
sidebar_position: 2
---

import TabI<PERSON> from "@theme/TabItem";
import Tabs from "@theme/Tabs";

For the best chat experience we recommend Claude 3.7 Sonnet from Anthropic.

Add Claude 3.7 Sonnet to your assistant from the hub [here](https://hub.continue.dev/anthropic/claude-3-7-sonnet). You can get an API key from the [Anthropic console](https://console.anthropic.com/settings/keys).

Other recommended chat models include:

- [GPT-4o from OpenAI](https://hub.continue.dev/openai/gpt-4o)
- [Grok-2 from xAI](https://hub.continue.dev/xai/grok-2)
- [Gemini 2.0 Flash from Google](https://hub.continue.dev/google/gemini-2.0-flash)
- [Llama 3.1 8b from Meta on Ollama](https://hub.continue.dev/ollama/llama3.1-8b) for a fully local experience
  {/* HUB_TODO nonexistent blocks */}
  {/* - Deepseek Coder 2 from deepseek on [Ollama](https://hub.continue.dev/explore/models) or [LM Studio](https://hub.continue.dev/explore/models) */}

For more details about setup and recommended models, see [here](../customize/model-roles/chat.mdx).
