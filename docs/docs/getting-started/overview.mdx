---
title: Overview
description: Getting started with Continue
keywords: [quickstart, start, overview, chat, autocomplete, edit, actions]
---

import Admonition from "@theme/Admonition";
import TabItem from "@theme/TabItem";
import Tabs from "@theme/Tabs";

To quickly understand what you can do with Continue, check out each tab below.

<Tabs>
  <TabItem value="chat" label="Chat">

    [Chat](../chat/how-to-use-it.md#how-to-use-it) makes it easy to ask for help from an LLM without needing to leave the IDE

    ![chat](/img/chat.gif)

    <Admonition type="info" title="Learn more" icon="📚">
        Learn more about [Chat](../chat/how-to-use-it.md#how-to-use-it)
    </Admonition>

  </TabItem>
  <TabItem value="autocomplete" label="Autocomplete">

    [Autocomplete](../autocomplete/how-to-use-it.md#how-to-use-it) provides inline code suggestions as you type

    ![autocomplete](/img/autocomplete.gif)

    <Admonition type="info" title="Learn more" icon="📚">
        Learn more about [Autocomplete](../autocomplete/how-to-use-it.md#how-to-use-it)
    </Admonition>

  </TabItem>

  <TabItem value="edit" label="Edit">

  [Edit](../edit/how-to-use-it.md#how-to-use-it) is a convenient way to modify code without leaving your current file

  ![edit](/img/edit.gif)

  <Admonition type="info" title="Learn more" icon="📚">
      Learn more about [Edit](../edit/how-to-use-it.md#how-to-use-it)
  </Admonition>

  </TabItem>

  <TabItem value="agent" label="Agent">

    [Agent](../agent/how-to-use-it.md#how-to-use-it) equips the Chat model with the tools needed to handle a wide range of coding tasks

    ![agent](/img/agent.gif)

    <Admonition type="info" title="Learn more" icon="📚">
        Learn more about [Agent](../agent/how-to-use-it.md#how-to-use-it)
    </Admonition>

  </TabItem>

</Tabs>
