---
title: Install
description: The open-source AI code assistant for developers
keywords: [install, installation, getting started, vs code, jetbrains]
---

## VS Code

1. Click `Install` on the [Continue extension page in the Visual Studio Marketplace](https://marketplace.visualstudio.com/items?itemName=Continue.continue)
2. This will open the Continue extension page in VS Code, where you will need to click `Install` again
3. The Continue logo will appear on the left sidebar. For a better experience, move Continue to the right sidebar
4. [Sign in to the hub](#signing-in) to get started with your first assistant.

![move-to-right-sidebar](/img/move-to-right-sidebar.gif)

:::info[Troubleshooting]
If you have any problems, see the [troubleshooting guide](../troubleshooting.mdx) or ask for help in [our Discord](https://discord.gg/NWtdYexhMs)
:::

## JetBrains

1. Open your JetBrains IDE and open **Settings** using <kbd>Ctrl</kbd> + <kbd>Alt</kbd> + <kbd>S</kbd>
2. Select **Plugins** on the sidebar and search for "Continue" in the marketplace
3. Click `Install`, which will cause the Continue logo to show up on the right toolbar
4. [Sign in to the hub](#signing-in) to get started with your first assistant.

![jetbrains-getting-started.png](/img/jetbrains-getting-started.png)

:::info[Troubleshooting]
If you have any problems, see the [troubleshooting guide](../troubleshooting.mdx) or ask for help in [our Discord](https://discord.com/invite/EfJEfdFnDQ)
:::

## Signing in

Click "Get started" to sign in to the hub and start using assistants.

![Hub Onboarding in the Extension](/img/hub/hub-onboarding-card.png)
