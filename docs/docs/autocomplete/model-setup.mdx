---
title: Model setup
description: Autocomplete \-model setup
keywords: [model, autocomplete]
sidebar_position: 2
---

import TabItem from "@theme/TabItem";
import Tabs from "@theme/Tabs";

For the best autocomplete experience we recommend Codestral from Mistral.

Add Codestral to your assistant from the hub [here](https://hub.continue.dev/mistral/codestral). Visit the [Mistral Platform](https://console.mistral.ai/codestral) to get a Codestral API key.

For a fully local autocomplete experience, we recommend [Qwen 2.5 Coder 1.5B on Ollama](https://hub.continue.dev/ollama/qwen2.5-coder-1.5b).

For more details about setup and recommended models, visit the [autocomplete deep dive](../customize/deep-dives/autocomplete.mdx).
