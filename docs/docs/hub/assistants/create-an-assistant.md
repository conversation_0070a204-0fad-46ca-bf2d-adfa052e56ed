---
title: Create an assistant
sidebar_label: Create an assistant
description: How to create a custom AI code assistant
keywords: [assistants, creation, guide]
---

# Create an assistant

## Remix an assistant

You should remix an assistant if you want to use it after some modifications.

By clicking the “remix” button, you’ll be taken to the “Create a remix” page.

![Remix Assistant Button](/img/hub/assistant-remix-button.png)

Once here, you’ll be able to

1. add or remove blocks in YAML configuration
2. change the name, description, icon, etc.

Clicking “Create assistant” will make this assistant available for use.

## Create an assistant from scratch

To create an assistant from scratch, select “New assistant” in the top bar.

![New assistant button](/img/hub/assistant-new-button.png)

Choose a name, slug, description, and icon for your assistant.

The easiest way to create an assistant is to click "Create assistant" with the default configuration and then add / remove blocks using the sidebar.

Alternatively, you can edit the assistant YAML directly before clicking "Create assistant". Refer to examples of assistants on [hub.continue.dev](https://hub.continue.dev/explore/assistants) and visit the [YAML Reference](../../reference.md#complete-yaml-config-example) docs for more details.

![New assistant YAML](/img/hub/assistant-create-yaml.png)
