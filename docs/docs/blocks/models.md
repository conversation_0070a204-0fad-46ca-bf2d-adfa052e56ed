---
title: Model Blocks
sidebar_label: Model
description: Foundation of the entire assistant experience offering specialized capabilities
keywords: [model, blocks, chat, edit, apply, autocomplete, embedding, reranker]
sidebar_position: 1
---

These blocks form the foundation of the entire assistant experience, offering different specialized capabilities:

- **[Chat](../customize/model-roles/chat.mdx)**: Power conversational interactions about code and provide detailed guidance
- **[Edit](../customize/model-roles/edit.mdx)**: Handle complex code transformations and refactoring tasks
- **[Apply](../customize/model-roles/apply.mdx)**: Execute targeted code modifications with high accuracy
- **[Autocomplete](../customize/model-roles/autocomplete.md)**: Provide real-time suggestions as developers type
- **[Embedding](../customize/model-roles/embeddings.mdx)**: Transform code into vector representations for semantic search
- **[Reranker](../customize/model-roles/reranking.mdx)**: Improve search relevance by ordering results based on semantic meaning

![Model Blocks Overview](/img/model-blocks-overview.png)

## Learn More

Continue supports [many model providers](../customize/model-providers), including Anthropic, OpenAI, Gemini, Ollama, Amazon Bedrock, Azure, xAI, DeepSeek, and more. Models can have various roles like `chat`, `edit`, `apply`, `autocomplete`, `embed`, and `rerank`.

Read more about roles [here](../customize/model-roles/intro.mdx) and view [`models`](../reference.md#models) in the YAML Reference.
