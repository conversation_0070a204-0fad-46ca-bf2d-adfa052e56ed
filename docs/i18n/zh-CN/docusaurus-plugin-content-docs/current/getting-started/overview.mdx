---
title: 概览
description: 开始使用 Continue
keywords: [快速开始, 开始, 概览, 聊天, 自动补全, 编辑, actions]
---

import Tabs from "@theme/Tabs";
import TabItem from "@theme/TabItem";
import Admonition from "@theme/Admonition";

为了快速理解你可以在 Continue 做的事，查看下面的每个标签页。

<Tabs>
  <TabItem value="chat" label="聊天">

    [聊天](../chat/how-to-use-it.md#如何使用它) 让你方便向 LLM 寻求帮助，而不需要离开 IDE

    ![chat](/img/chat.gif)

    <Admonition type="info" title="了解更多" icon="📚">
        了解更多关于 [聊天](../chat/how-to-use-it.md#如何使用它)
    </Admonition>

  </TabItem>
  <TabItem value="autocomplete" label="自动补全">

    [自动补全](../autocomplete/how-to-use-it.md#如何使用它) 随着你的输入提供行内代码建议

    ![autocomplete](/img/autocomplete.gif)

    <Admonition type="info" title="了解更多" icon="📚">
        了解更多关于 [自动补全](../autocomplete/how-to-use-it.md#如何使用它)
    </Admonition>

  </TabItem>
  <TabItem value="edit" label="编辑">

    [编辑](../edit/how-to-use-it.md#如何使用它) 是一种编辑代码的方便的方式，不需要离开你的当前文件

    ![edit](/img/edit.gif)

    <Admonition type="info" title="了解更多" icon="📚">
        了解更多关于 [编辑](../edit/how-to-use-it.md#如何使用它)
    </Admonition>

  </TabItem>
  <TabItem value="s" label="Actions">

    [Actions](../actions/how-to-use-it.md#如何使用它) 是常用情况的快捷方式

    ![actions](/img/actions.gif)

    <Admonition type="info" title="了解更多" icon="📚">
        了解更多关于如何使用 [Actions](../actions/how-to-use-it.md#如何使用它)
    </Admonition>

  </TabItem>
</Tabs>
