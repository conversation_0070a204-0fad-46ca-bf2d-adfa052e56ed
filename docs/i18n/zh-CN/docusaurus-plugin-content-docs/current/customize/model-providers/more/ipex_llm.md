# IPEX-LLM

:::info
[**IPEX-LLM**](https://github.com/intel-analytics/ipex-llm) 是一个 PyTorch 库，用来在 Intel CPU 和 GPU 上运行 LLM ，(例如，有 iGPU 的 PC ，独立 GPU ，比如， Arc A-Series, Flex 和 Max)，有着很低的延迟。
:::

IPEX-LLM 支持加速托管在 Intel GPU 上的 Ollama 后端。从 IPEX-LLM 官方文档查看 [这个指南](https://ipex-llm.readthedocs.io/en/latest/doc/LLM/Quickstart/ollama_quickstart.html)，关于如何安装和运行通过 IPEX-LLM 在 Intel GPU 上的 Ollama 服务加速。你可以如下配置 Continue 使用 IPEX-LLM 加速 `"ollama"` 提供者：

```json title="config.json"
{
  "models": [
    {
      "title": "IPEX-LLM",
      "provider": "ollama",
      "model": "AUTODETECT"
    }
  ]
}
```

如果你想要访问来自其他机器的 Ollama 服务，确保你在执行 `ollama serve` 命令之前， set 或 export 环境变量 `OLLAMA_HOST=0.0.0.0` 。然后，在 Continue 配置中，设置 `'apiBase'` 与远程机器的 IP 地址 / 端口一致。也就是， Continue 可以配置为：

```json title="config.json"
{
  "models": [
    {
      "title": "IPEX-LLM",
      "provider": "ollama",
      "model": "AUTODETECT",
      "apiBase": "http://your-ollama-service-ip:11434"
    }
  ]
}
```

:::tip
如果你想要在 Continue 中第一次与模型交谈之前预加载模型，你需要查看 [这里](https://ipex-llm.readthedocs.io/en/latest/doc/LLM/Quickstart/continue_quickstart.html#pull-and-prepare-the-model) 获取更多信息。
:::
