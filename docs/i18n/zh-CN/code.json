{"theme.ErrorPageContent.title": {"message": "页面已崩溃。", "description": "The title of the fallback page when the page crashed"}, "theme.BackToTopButton.buttonAriaLabel": {"message": "回到顶部", "description": "The ARIA label for the back to top button"}, "theme.blog.archive.title": {"message": "历史博文", "description": "The page & hero title of the blog archive page"}, "theme.blog.archive.description": {"message": "历史博文", "description": "The page & hero description of the blog archive page"}, "theme.blog.paginator.navAriaLabel": {"message": "博文列表分页导航", "description": "The ARIA label for the blog pagination"}, "theme.blog.paginator.newerEntries": {"message": "较新的博文", "description": "The label used to navigate to the newer blog posts page (previous page)"}, "theme.blog.paginator.olderEntries": {"message": "较旧的博文", "description": "The label used to navigate to the older blog posts page (next page)"}, "theme.blog.post.paginator.navAriaLabel": {"message": "博文分页导航", "description": "The ARIA label for the blog posts pagination"}, "theme.blog.post.paginator.newerPost": {"message": "较新一篇", "description": "The blog post button label to navigate to the newer/previous post"}, "theme.blog.post.paginator.olderPost": {"message": "较旧一篇", "description": "The blog post button label to navigate to the older/next post"}, "theme.tags.tagsPageLink": {"message": "查看所有标签", "description": "The label of the link targeting the tag list page"}, "theme.colorToggle.ariaLabel": {"message": "切换浅色/暗黑模式（当前为{mode}）", "description": "The ARIA label for the navbar color mode toggle"}, "theme.colorToggle.ariaLabel.mode.dark": {"message": "暗黑模式", "description": "The name for the dark color mode"}, "theme.colorToggle.ariaLabel.mode.light": {"message": "浅色模式", "description": "The name for the light color mode"}, "theme.docs.breadcrumbs.navAriaLabel": {"message": "页面路径", "description": "The ARIA label for the breadcrumbs"}, "theme.docs.DocCard.categoryDescription.plurals": {"message": "{count} 个项目", "description": "The default description for a category card in the generated index about how many items this category includes"}, "theme.docs.paginator.navAriaLabel": {"message": "文件选项卡", "description": "The ARIA label for the docs pagination"}, "theme.docs.paginator.previous": {"message": "上一页", "description": "The label used to navigate to the previous doc"}, "theme.docs.paginator.next": {"message": "下一页", "description": "The label used to navigate to the next doc"}, "theme.docs.tagDocListPageTitle.nDocsTagged": {"message": "{count} 篇文档带有标签", "description": "Pluralized label for \"{count} docs tagged\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.docs.tagDocListPageTitle": {"message": "{nDocsTagged}「{tagName}」", "description": "The title of the page for a docs tag"}, "theme.docs.versionBadge.label": {"message": "版本：{versionLabel}"}, "theme.docs.versions.unreleasedVersionLabel": {"message": "此为 {siteTitle} {versionLabel} 版尚未发行的文档。", "description": "The label used to tell the user that he's browsing an unreleased doc version"}, "theme.docs.versions.unmaintainedVersionLabel": {"message": "此为 {siteTitle} {versionLabel} 版的文档，现已不再积极维护。", "description": "The label used to tell the user that he's browsing an unmaintained doc version"}, "theme.docs.versions.latestVersionSuggestionLabel": {"message": "最新的文档请参阅 {latestVersionLink} ({versionLabel})。", "description": "The label used to tell the user to check the latest version"}, "theme.docs.versions.latestVersionLinkLabel": {"message": "最新版本", "description": "The label used for the latest version suggestion link label"}, "theme.common.editThisPage": {"message": "编辑此页", "description": "The link label to edit the current page"}, "theme.common.headingLinkTitle": {"message": "{heading}的直接链接", "description": "Title for link to heading"}, "theme.lastUpdated.atDate": {"message": "于 {date} ", "description": "The words used to describe on which date a page has been last updated"}, "theme.lastUpdated.byUser": {"message": "由 {user} ", "description": "The words used to describe by who the page has been last updated"}, "theme.lastUpdated.lastUpdatedAtBy": {"message": "最后{byUser}{atDate}更新", "description": "The sentence used to display when a page has been last updated, and by who"}, "theme.navbar.mobileVersionsDropdown.label": {"message": "选择版本", "description": "The label for the navbar versions dropdown on mobile view"}, "theme.NotFound.title": {"message": "找不到页面", "description": "The title of the 404 page"}, "theme.tags.tagsListLabel": {"message": "标签：", "description": "The label alongside a tag list"}, "theme.AnnouncementBar.closeButtonAriaLabel": {"message": "关闭", "description": "The ARIA label for close button of announcement bar"}, "theme.admonition.caution": {"message": "警告", "description": "The default label used for the Caution admonition (:::caution)"}, "theme.admonition.danger": {"message": "危险", "description": "The default label used for the Danger admonition (:::danger)"}, "theme.admonition.info": {"message": "信息", "description": "The default label used for the Info admonition (:::info)"}, "theme.admonition.note": {"message": "备注", "description": "The default label used for the Note admonition (:::note)"}, "theme.admonition.tip": {"message": "提示", "description": "The default label used for the Tip admonition (:::tip)"}, "theme.admonition.warning": {"message": "注意", "description": "The default label used for the Warning admonition (:::warning)"}, "theme.blog.sidebar.navAriaLabel": {"message": "最近博文导航", "description": "The ARIA label for recent posts in the blog sidebar"}, "theme.CodeBlock.copied": {"message": "复制成功", "description": "The copied button label on code blocks"}, "theme.CodeBlock.copyButtonAriaLabel": {"message": "复制代码到剪贴板", "description": "The ARIA label for copy code blocks button"}, "theme.CodeBlock.copy": {"message": "复制", "description": "The copy button label on code blocks"}, "theme.CodeBlock.wordWrapToggle": {"message": "切换自动换行", "description": "The title attribute for toggle word wrapping button of code block lines"}, "theme.DocSidebarItem.expandCategoryAriaLabel": {"message": "展开侧边栏分类 '{label}'", "description": "The ARIA label to expand the sidebar category"}, "theme.DocSidebarItem.collapseCategoryAriaLabel": {"message": "折叠侧边栏分类 '{label}'", "description": "The ARIA label to collapse the sidebar category"}, "theme.NavBar.navAriaLabel": {"message": "主导航", "description": "The ARIA label for the main navigation"}, "theme.navbar.mobileLanguageDropdown.label": {"message": "选择语言", "description": "The label for the mobile language switcher dropdown"}, "theme.NotFound.p1": {"message": "我们找不到您要找的页面。", "description": "The first paragraph of the 404 page"}, "theme.NotFound.p2": {"message": "请联系原始链接来源网站的所有者，并告知他们链接已损坏。", "description": "The 2nd paragraph of the 404 page"}, "theme.TOCCollapsible.toggleButtonLabel": {"message": "本页总览", "description": "The label used by the button on the collapsible TOC component"}, "theme.blog.post.readingTime.plurals": {"message": "阅读需 {readingTime} 分钟", "description": "Pluralized label for \"{readingTime} min read\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.blog.post.readMore": {"message": "阅读更多", "description": "The label used in blog post item excerpts to link to full blog posts"}, "theme.blog.post.readMoreLabel": {"message": "阅读 {title} 的全文", "description": "The ARIA label for the link to full blog posts from excerpts"}, "theme.docs.sidebar.collapseButtonTitle": {"message": "收起侧边栏", "description": "The title attribute for collapse button of doc sidebar"}, "theme.docs.sidebar.collapseButtonAriaLabel": {"message": "收起侧边栏", "description": "The title attribute for collapse button of doc sidebar"}, "theme.docs.breadcrumbs.home": {"message": "主页面", "description": "The ARIA label for the home page in the breadcrumbs"}, "theme.docs.sidebar.navAriaLabel": {"message": "文档侧边栏", "description": "The ARIA label for the sidebar navigation"}, "theme.docs.sidebar.closeSidebarButtonAriaLabel": {"message": "关闭导航栏", "description": "The ARIA label for close button of mobile sidebar"}, "theme.navbar.mobileSidebarSecondaryMenu.backButtonLabel": {"message": "← 回到主菜单", "description": "The label of the back button to return to main menu, inside the mobile navbar sidebar secondary menu (notably used to display the docs sidebar)"}, "theme.docs.sidebar.toggleSidebarButtonAriaLabel": {"message": "切换导航栏", "description": "The ARIA label for hamburger menu button of mobile navigation"}, "theme.docs.sidebar.expandButtonTitle": {"message": "展开侧边栏", "description": "The ARIA label and title attribute for expand button of doc sidebar"}, "theme.docs.sidebar.expandButtonAriaLabel": {"message": "展开侧边栏", "description": "The ARIA label and title attribute for expand button of doc sidebar"}, "theme.SearchBar.seeAll": {"message": "查看全部 {count} 个结果"}, "theme.SearchPage.documentsFound.plurals": {"message": "找到 {count} 份文件", "description": "Pluralized label for \"{count} documents found\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.SearchPage.existingResultsTitle": {"message": "「{query}」的搜索结果", "description": "The search page title for non-empty query"}, "theme.SearchPage.emptyResultsTitle": {"message": "在文档中搜索", "description": "The search page title for empty query"}, "theme.SearchPage.inputPlaceholder": {"message": "在此输入搜索字词", "description": "The placeholder for search page input"}, "theme.SearchPage.inputLabel": {"message": "搜索", "description": "The ARIA label for search page input"}, "theme.SearchPage.algoliaLabel": {"message": "通过 Algolia 搜索", "description": "The ARIA label for Algolia mention"}, "theme.SearchPage.noResultsText": {"message": "未找到任何结果", "description": "The paragraph for empty search result"}, "theme.SearchPage.fetchingNewResults": {"message": "正在获取新的搜索结果...", "description": "The paragraph for fetching new search results"}, "theme.SearchBar.label": {"message": "搜索", "description": "The ARIA label and placeholder for search button"}, "theme.SearchModal.searchBox.resetButtonTitle": {"message": "清除查询", "description": "The label and ARIA label for search box reset button"}, "theme.SearchModal.searchBox.cancelButtonText": {"message": "取消", "description": "The label and ARIA label for search box cancel button"}, "theme.SearchModal.startScreen.recentSearchesTitle": {"message": "最近搜索", "description": "The title for recent searches"}, "theme.SearchModal.startScreen.noRecentSearchesText": {"message": "没有最近搜索", "description": "The text when no recent searches"}, "theme.SearchModal.startScreen.saveRecentSearchButtonTitle": {"message": "保存这个搜索", "description": "The label for save recent search button"}, "theme.SearchModal.startScreen.removeRecentSearchButtonTitle": {"message": "从历史记录中删除这个搜索", "description": "The label for remove recent search button"}, "theme.SearchModal.startScreen.favoriteSearchesTitle": {"message": "收藏", "description": "The title for favorite searches"}, "theme.SearchModal.startScreen.removeFavoriteSearchButtonTitle": {"message": "从收藏列表中删除这个搜索", "description": "The label for remove favorite search button"}, "theme.SearchModal.errorScreen.titleText": {"message": "无法获取结果", "description": "The title for error screen of search modal"}, "theme.SearchModal.errorScreen.helpText": {"message": "你可能需要检查网络连接。", "description": "The help text for error screen of search modal"}, "theme.SearchModal.footer.selectText": {"message": "选中", "description": "The explanatory text of the action for the enter key"}, "theme.SearchModal.footer.selectKeyAriaLabel": {"message": "Enter 键", "description": "The ARIA label for the Enter key button that makes the selection"}, "theme.SearchModal.footer.navigateText": {"message": "导航", "description": "The explanatory text of the action for the Arrow up and Arrow down key"}, "theme.SearchModal.footer.navigateUpKeyAriaLabel": {"message": "向上键", "description": "The ARIA label for the Arrow up key button that makes the navigation"}, "theme.SearchModal.footer.navigateDownKeyAriaLabel": {"message": "向下键", "description": "The ARIA label for the Arrow down key button that makes the navigation"}, "theme.SearchModal.footer.closeText": {"message": "关闭", "description": "The explanatory text of the action for Escape key"}, "theme.SearchModal.footer.closeKeyAriaLabel": {"message": "Esc 键", "description": "The ARIA label for the Escape key button that close the modal"}, "theme.SearchModal.footer.searchByText": {"message": "搜索提供", "description": "The text explain that the search is making by Algolia"}, "theme.SearchModal.noResultsScreen.noResultsText": {"message": "没有结果：", "description": "The text explains that there are no results for the following search"}, "theme.SearchModal.noResultsScreen.suggestedQueryText": {"message": "试试搜索", "description": "The text for the suggested query when no results are found for the following search"}, "theme.SearchModal.noResultsScreen.reportMissingResultsText": {"message": "认为这个查询应该有结果？", "description": "The text for the question where the user thinks there are missing results"}, "theme.SearchModal.noResultsScreen.reportMissingResultsLinkText": {"message": "请告知我们。", "description": "The text for the link to report missing results"}, "theme.SearchModal.placeholder": {"message": "搜索文档", "description": "The placeholder of the input of the DocSearch pop-up modal"}, "theme.blog.post.plurals": {"message": "{count} 篇博文", "description": "Pluralized label for \"{count} posts\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.blog.tagTitle": {"message": "{nPosts} 含有标签「{tagName}」", "description": "The title of the page for a blog tag"}, "theme.blog.author.pageTitle": {"message": "{authorName} - {nPosts}", "description": "The title of the page for a blog author"}, "theme.blog.authorsList.pageTitle": {"message": "Authors", "description": "The title of the authors page"}, "theme.blog.authorsList.viewAll": {"message": "View All Authors", "description": "The label of the link targeting the blog authors page"}, "theme.contentVisibility.unlistedBanner.title": {"message": "未列出页", "description": "The unlisted content banner title"}, "theme.contentVisibility.unlistedBanner.message": {"message": "此页面未列出。搜索引擎不会对其索引，只有拥有直接链接的用户才能访问。", "description": "The unlisted content banner message"}, "theme.contentVisibility.draftBanner.title": {"message": "Draft page", "description": "The draft content banner title"}, "theme.contentVisibility.draftBanner.message": {"message": "This page is a draft. It will only be visible in dev and be excluded from the production build.", "description": "The draft content banner message"}, "theme.ErrorPageContent.tryAgain": {"message": "重试", "description": "The label of the button to try again rendering when the React error boundary captures an error"}, "theme.common.skipToMainContent": {"message": "跳到主要内容", "description": "The skip to content label used for accessibility, allowing to rapidly navigate to main content with keyboard tab/enter navigation"}, "theme.tags.tagsPageTitle": {"message": "标签", "description": "The title of the tag list page"}}