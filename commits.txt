fix: 访问后端API时新增'Authorization'请求头。
refactor:1. 更新ConfigHandler.ts中API请求的IP地址，替换localhost为**************。2. 调整代码格式，增加分号以提高可读性。3. 更新ContinueAuthService.kt中的API URL以匹配新的IP地址。
Merge remote-tracking branch 'yinhai-origin/feat_login' into yinhai_dev
feat:1. 调整OpenAI.ts中maxEmbeddingChunkSize参数为1000，并添加dimensions参数以支持更高维度嵌入。2. 将ConfirmationDialog和IndexingProgress相关界面文案更新为中文，优化IDEA和VSCode的索引重建确认流程。
fix: 调整模块选择工具，当返回的模块名称正确路径错误时，修复模块路径；调整代码片段打分工具，控制llm调用并发最大为4.
feat: 添加重置功能到sessionSlice1. 在sessionSlice.ts中增加了重置结构化智能体工作流程状态的功能。2. 初始化structuredAgentWorkflow状态，包括是否激活、当前步骤、步骤总数等。
feat: 加入工具类方法获取，开发约束获取
feat: 优化StepContainer和workflow逻辑1. 修改StepContainer组件中textarea高度及禁止滚动条的出现。2. 改善structuredAgentWorkflow中步骤的中英文说明逻辑。3. 增加获取会话历史记录和提取工具返回结果的辅助方法。4. 简化工具调用结果处理流程，统一获取最近工具调用输出。
refactor: 简化确认指令关键字列表1. 在文件structuredAgentWorkflow.ts中，简化了确认指令的关键字列表，移除了“继续”和“下一步”等多余关键字。2. 优化关键字检测的准确性，更聚焦于常用的确认术语。
refactor:1. 优化StructuredAgentProgress.tsx，改进代码结构和样式，提高代码可读性。2. 修改structuredAgentWorkflow.ts中确认提示的输出格式，并在流程终点增加等待用户确认的步骤。
feat:1. 在codeChunkAnalysis.ts和projectAnalysis.ts中添加对用户反馈的处理逻辑。2. 更新界面组件以支持中英文切换，包括ContinueLogo.tsx、CopyIconButton.tsx、ResponseActions.tsx等。3. 在error.tsx中调整错误页面的显示文本和导航行为。4. 优化相关状态管理逻辑，与redux相关的thunk进行了调整以传递用户反馈数据。
feat:1. 在ResponseActions.tsx中添加编辑功能，支持通过PencilIcon进行消息编辑。2. 在StepContainer.tsx中实现编辑状态和保存、取消编辑的功能。3. 在sessionSlice.ts中添加editMessage action，用于更新消息内容。
feat: 优化和重构项目分析与代码片段分析工具1. 引入contextData以传递额外的上下文数据，提升结构化智能体模式的工具交互2. 修改代码片段分析，优先从contextData中获取参数，取消向量化分析3. 简化project_analysis和code_chunk_analysis工具的参数传递逻辑，默认使用contextData数据4. 改进structuredAgentWorkflow的提示和步骤管理，重新激活所有内置工具使用权
Merge remote-tracking branch 'origin/yinhai_dev' into yinhai_dev
fix: 提示词和方法调整，代码片段选取时，一次性传入所有模块和代码文件
feat: 优化结构化智能体工具选择与工作流1. 根据当前步骤限制结构化智能体使用的工具，增强工具选择的灵活性。2. 增加多步骤的工具调用结果获取逻辑，支持多个结果汇总。3. 将部分界面文本更改为中文，提升用户体验。
新增功能：1. 在`sessionSlice.ts`中，添加了`stepHistoryStartIndex`用于记录步骤历史索引，支持结构化智能体模式下的多轮对话；2. 在`structuredAgentWorkflow.ts`中，动态生成系统消息，增加详细需求的传递与步骤处理结果的提取；3. 实现对工具调用结果的历史记录片段提取，提高代码分析和计划执行的准确性。
fix: 与后端对接了获取智能体配置接口。
feat:1. 更新多个SVG图标的颜色样式，更改为新的配色方案。2. 移除SVG文件中的滤镜定义以简化图标设计。3. 保持图标功能一致，仅对视觉元素进行调整，提升视觉一致性。
feat:1. 将StructuredAgentProgress组件的样式改为使用变量从主题中获取颜色，以增强可定制性。2. 在结构化Agent的thunk中新增promptPreamble字段，根据用户输入在需求分解和用户反馈时动态调整提示语。
feat: 更新日志管理依赖1. 添加SLF4J到Log4j2.x的桥接实现，优化日志记录兼容性。
refactor: 优化系统消息和状态管理
fix: 登录完成后更新界面登录状态。以往遗漏了更新界面步骤。
fix: 实现了登录流程与银海通集成
1. **refactor**: 简化了constructMessages.ts中的系统消息合并逻辑，去掉不必要的注释，提高代码可读性。 2. **refactor**: codeChunkAnalysis.ts更新了工具名称和参数描述，以提高模块化分析精度。 3. **fix**: 修正了projectAnalysis.ts中需求参数的必填状态，确保操作准确。 4. **fix**: 修改codeChunkAnalyzer.ts JSON解析逻辑，明确json输出，不再使用代码块格式。 5. **feat**: structuredAgentWorkflow.ts中完善了需求拆解步骤，增强了系统提示和解析功能。
feat(core): 添加代码片段分析工具并优化项目分析功能
feat(core): 添加项目分析工具- 新增 ProjectAnalysis 工具用于分析 Maven 项目结构 - 实现项目结构解析、模块推荐和文件分析功能 -集成 LLM 以提供智能推荐 - 更新相关文件以支持新工具
feat(core): 添加结构化智能体模式
fix: 从服务端读取智能体列表
feat(llm): 添加 LocalQwen 模型支持
fix: 登录时从银海通获取UID
refactor(gui): 中文化图形界面并优化用户体验
feat(core): 新增配置项以在聊天模式下保留工具调用
refactor(core): 优化嵌入生成逻辑并更新 API 端点
refactor(extension): 优化代码和资源
1. refactor: 修改`CommitMessageGenerationAction.java`，统一Icon命名为全大写格式。 2. fix: 调整`TaAiPresentation.java`中图标渲染逻辑，使用统一的静态常量EX_ICON。 3. fix: 更新`ta3logo_ex.svg`文件格式，调整SVG尺寸与代码样式以提高可读性。
feat(intellij): 添加 Java 异常调试功能
refactor(intellij): 重构提交信息生成功能
fix: 修改后端地址与相关环境配置，对接155线上地址，实现基础的登录和llm配置信息加载，openai和claude代理访问能力
fix: 调整install脚本，只需要终端配置代理即可完成install，不需要手动粘贴下载文件
feat(intellij): 添加生成提交信息功能
refactor(core): 新增快捷操作，中英翻译功能
refactor(intellij): 重构 IntelliJ 插件并更新品牌信息- 更新插件名称和标识符为 TA+3AI - 修改厂商信息为 研发部-AI开发技术组 - 更新插件图标 - 调整工具窗口名称 - 更新相关文件中的插件 ID