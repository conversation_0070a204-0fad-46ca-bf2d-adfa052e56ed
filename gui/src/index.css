@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: "Inter";
  src: url("/fonts/Inter/InterDisplay-Light.woff2") format("woff2");
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: "JetBrains Mono";
  src: url("/fonts/JetBrainsMono/JetBrainsMono-Light.woff2") format("woff2");
  font-weight: 300;
  font-style: normal;
}

:root {
  --example-var: rgb(30, 30, 30);
}

html,
body,
#root {
  height: 100%;
  line-height: 1.3;
  background-color: var(--vscode-editor-background);
  font-family:
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    "Open Sans",
    "Helvetica Neue",
    sans-serif;
}

body {
  padding: 0;
  color: var(--vscode-editor-foreground);
  padding: 0px;
  margin: 0px;
  height: 100%;
}

/* To override the default `-webkit-focus-ring-color` that is an ugly orange */
*:focus {
  outline: none;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fade-in-span {
  animation: fadeIn 0.3s ease-in-out;
}

.thin-scrollbar {
  scrollbar-width: thin;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.no-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.truncate-start {
  display: inline-block;
  vertical-align: middle;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  direction: rtl;
  width: 100%;
}

.scroll-container {
  overflow-y: auto; /* Enable vertical scrolling only if needed */
  scrollbar-width: thin; /* Firefox */
  padding-right: 4px;
}

.scroll-container::-webkit-scrollbar {
  width: 8px;
}

.scroll-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.scroll-container::-webkit-scrollbar-thumb {
  background-color: #888;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.scroll-container::-webkit-scrollbar-thumb:hover {
  background-color: #555;
}

.scroll-container:is(:hover, :focus)::-webkit-scrollbar {
  display: block; /* Show scrollbar only on hover or focus */
}
