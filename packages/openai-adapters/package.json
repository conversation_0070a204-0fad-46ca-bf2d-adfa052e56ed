{"name": "@continuedev/openai-adapters", "version": "1.0.26", "description": "", "main": "dist/index.js", "types": "dist/index.d.ts", "type": "module", "scripts": {"test": "cross-env NODE_OPTIONS=--experimental-vm-modules jest", "build": "tsc"}, "author": "<PERSON> and <PERSON>", "license": "Apache-2.0", "dependencies": {"@azure/openai": "^1.0.0-beta.12", "@continuedev/config-types": "^1.0.5", "@continuedev/config-yaml": "^1.0.51", "@continuedev/fetch": "^1.0.10", "dotenv": "^16.5.0", "json-schema": "^0.4.0", "node-fetch": "^3.3.2", "openai": "^4.76.0", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@types/follow-redirects": "^1.14.4", "@types/jest": "^29.5.14", "@types/json-schema": "^7.0.15", "cross-env": "^7.0.3", "ts-jest": "^29.2.3", "ts-node": "^10.9.2"}}