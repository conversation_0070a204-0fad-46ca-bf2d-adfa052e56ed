{
  "compilerOptions": {
    "target": "ESNext",
    "module": "NodeNext",
    "useDefineForClassFields": true,
    "lib": ["DOM", "ESNext"],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "moduleResolution": "NodeNext",
    "resolveJsonModule": true,
    "isolatedModules": false,
    "noEmitOnError": false,
    "types": ["node", "jest"],
    "outDir": "dist",
    "declaration": true
    // "sourceMap": true
  },
  "include": ["src/**/*"]
}
