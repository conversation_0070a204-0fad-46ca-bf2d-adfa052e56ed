# ref: https://docs.travis-ci.com/user/languages/python
language: python
python:
  - "3.8"
  - "3.9"
  - "3.10"
  - "3.11"
  - "3.12"
  # uncomment the following if needed
  #- "3.12-dev"  # 3.12 development branch
  #- "nightly"  # nightly build
# command to install dependencies
install:
  - "pip install -r requirements.txt"
  - "pip install -r test-requirements.txt"
# command to run tests
script: pytest --cov=openapi_client
