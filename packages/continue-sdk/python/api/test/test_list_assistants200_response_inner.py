# coding: utf-8

"""
    Continue Hub IDE API

    API for Continue IDE to fetch assistants and other related information. These endpoints are primarily used by the Continue IDE extensions for VS Code and JetBrains. 

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from openapi_client.models.list_assistants200_response_inner import ListAssistants200ResponseInner

class TestListAssistants200ResponseInner(unittest.TestCase):
    """ListAssistants200ResponseInner unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> ListAssistants200ResponseInner:
        """Test ListAssistants200ResponseInner
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `ListAssistants200ResponseInner`
        """
        model = ListAssistants200ResponseInner()
        if include_optional:
            return ListAssistants200ResponseInner(
                config_result = openapi_client.models.list_assistants_200_response_inner_config_result.listAssistants_200_response_inner_configResult(
                    config = openapi_client.models.config.config(), 
                    config_load_interrupted = True, 
                    errors = [
                        ''
                        ], ),
                owner_slug = '',
                package_slug = '',
                icon_url = '',
                on_prem_proxy_url = '',
                use_on_prem_proxy = True,
                raw_yaml = ''
            )
        else:
            return ListAssistants200ResponseInner(
                config_result = openapi_client.models.list_assistants_200_response_inner_config_result.listAssistants_200_response_inner_configResult(
                    config = openapi_client.models.config.config(), 
                    config_load_interrupted = True, 
                    errors = [
                        ''
                        ], ),
                owner_slug = '',
                package_slug = '',
        )
        """

    def testListAssistants200ResponseInner(self):
        """Test ListAssistants200ResponseInner"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
