# ListAssistants401Response

## Properties

| Name        | Type    | Description | Notes      |
| ----------- | ------- | ----------- | ---------- |
| **message** | **str** |             | [optional] |

## Example

```python
from openapi_client.models.list_assistants401_response import ListAssistants401Response

# TODO update the JSON string below
json = "{}"
# create an instance of ListAssistants401Response from a JSON string
list_assistants401_response_instance = ListAssistants401Response.from_json(json)
# print the JSON string representation of the object
print(ListAssistants401Response.to_json())

# convert the object into a dict
list_assistants401_response_dict = list_assistants401_response_instance.to_dict()
# create an instance of ListAssistants401Response from a dict
list_assistants401_response_from_dict = ListAssistants401Response.from_dict(list_assistants401_response_dict)
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
