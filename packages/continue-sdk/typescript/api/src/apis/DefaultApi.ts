/* tslint:disable */
/* eslint-disable */
/**
 * Continue Hub IDE API
 * API for Continue IDE to fetch assistants and other related information. These endpoints are primarily used by the Continue IDE extensions for VS Code and JetBrains.
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import * as runtime from "../runtime";
import type {
  ListAssistants200ResponseInner,
  ListAssistants401Response,
  ListAssistants404Response,
} from "../models/index";
import {
  ListAssistants200ResponseInnerFromJSON,
  ListAssistants200ResponseInnerToJSON,
  ListAssistants401ResponseFromJSON,
  ListAssistants401ResponseToJSON,
  ListAssistants404ResponseFromJSON,
  ListAssistants404ResponseToJSON,
} from "../models/index";

export interface ListAssistantsRequest {
  alwaysUseProxy?: ListAssistantsAlwaysUseProxyEnum;
  organizationId?: string;
}

/**
 * DefaultApi - interface
 *
 * @export
 * @interface DefaultApiInterface
 */
export interface DefaultApiInterface {
  /**
   * Returns a complete list of assistants available to the user, with their full configurations, icons, and other metadata needed by the IDE to display and use them.  This endpoint performs a full refresh of the list of assistants, including unrolling configurations and resolving secrets.
   * @summary List assistants for IDE
   * @param {'true' | 'false'} [alwaysUseProxy] Whether to always use the Continue-managed proxy for model requests
   * @param {string} [organizationId] ID of the organization to scope assistants to. If not provided, personal assistants are returned.
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApiInterface
   */
  listAssistantsRaw(
    requestParameters: ListAssistantsRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<Array<ListAssistants200ResponseInner>>>;

  /**
   * Returns a complete list of assistants available to the user, with their full configurations, icons, and other metadata needed by the IDE to display and use them.  This endpoint performs a full refresh of the list of assistants, including unrolling configurations and resolving secrets.
   * List assistants for IDE
   */
  listAssistants(
    requestParameters: ListAssistantsRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<Array<ListAssistants200ResponseInner>>;
}

/**
 *
 */
export class DefaultApi extends runtime.BaseAPI implements DefaultApiInterface {
  /**
   * Returns a complete list of assistants available to the user, with their full configurations, icons, and other metadata needed by the IDE to display and use them.  This endpoint performs a full refresh of the list of assistants, including unrolling configurations and resolving secrets.
   * List assistants for IDE
   */
  async listAssistantsRaw(
    requestParameters: ListAssistantsRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<Array<ListAssistants200ResponseInner>>> {
    const queryParameters: any = {};

    if (requestParameters["alwaysUseProxy"] != null) {
      queryParameters["alwaysUseProxy"] = requestParameters["alwaysUseProxy"];
    }

    if (requestParameters["organizationId"] != null) {
      queryParameters["organizationId"] = requestParameters["organizationId"];
    }

    const headerParameters: runtime.HTTPHeaders = {};

    if (this.configuration && this.configuration.accessToken) {
      const token = this.configuration.accessToken;
      const tokenString = await token("apiKeyAuth", []);

      if (tokenString) {
        headerParameters["Authorization"] = `Bearer ${tokenString}`;
      }
    }
    const response = await this.request(
      {
        path: `/ide/list-assistants`,
        method: "GET",
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      jsonValue.map(ListAssistants200ResponseInnerFromJSON),
    );
  }

  /**
   * Returns a complete list of assistants available to the user, with their full configurations, icons, and other metadata needed by the IDE to display and use them.  This endpoint performs a full refresh of the list of assistants, including unrolling configurations and resolving secrets.
   * List assistants for IDE
   */
  async listAssistants(
    requestParameters: ListAssistantsRequest = {},
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<Array<ListAssistants200ResponseInner>> {
    const response = await this.listAssistantsRaw(
      requestParameters,
      initOverrides,
    );
    return await response.value();
  }
}

/**
 * @export
 */
export const ListAssistantsAlwaysUseProxyEnum = {
  TRUE: "true",
  FALSE: "false",
} as const;
export type ListAssistantsAlwaysUseProxyEnum =
  (typeof ListAssistantsAlwaysUseProxyEnum)[keyof typeof ListAssistantsAlwaysUseProxyEnum];
