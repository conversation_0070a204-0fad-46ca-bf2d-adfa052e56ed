/* tslint:disable */
/* eslint-disable */
/**
 * Continue Hub IDE API
 * API for Continue IDE to fetch assistants and other related information. These endpoints are primarily used by the Continue IDE extensions for VS Code and JetBrains.
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from "../runtime";
/**
 *
 * @export
 * @interface ListAssistants401Response
 */
export interface ListAssistants401Response {
  /**
   *
   * @type {string}
   * @memberof ListAssistants401Response
   */
  message?: string;
}

/**
 * Check if a given object implements the ListAssistants401Response interface.
 */
export function instanceOfListAssistants401Response(
  value: object,
): value is ListAssistants401Response {
  return true;
}

export function ListAssistants401ResponseFromJSON(
  json: any,
): ListAssistants401Response {
  return ListAssistants401ResponseFromJSONTyped(json, false);
}

export function ListAssistants401ResponseFromJSONTyped(
  json: any,
  ignoreDiscriminator: boolean,
): ListAssistants401Response {
  if (json == null) {
    return json;
  }
  return {
    message: json["message"] == null ? undefined : json["message"],
  };
}

export function ListAssistants401ResponseToJSON(
  json: any,
): ListAssistants401Response {
  return ListAssistants401ResponseToJSONTyped(json, false);
}

export function ListAssistants401ResponseToJSONTyped(
  value?: ListAssistants401Response | null,
  ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    message: value["message"],
  };
}
