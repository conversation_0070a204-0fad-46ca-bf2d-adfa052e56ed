/* tslint:disable */
/* eslint-disable */
/**
 * Continue Hub IDE API
 * API for Continue IDE to fetch assistants and other related information. These endpoints are primarily used by the Continue IDE extensions for VS Code and JetBrains.
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from "../runtime";
/**
 *
 * @export
 * @interface ListAssistants404Response
 */
export interface ListAssistants404Response {
  /**
   *
   * @type {string}
   * @memberof ListAssistants404Response
   */
  message?: string;
}

/**
 * Check if a given object implements the ListAssistants404Response interface.
 */
export function instanceOfListAssistants404Response(
  value: object,
): value is ListAssistants404Response {
  return true;
}

export function ListAssistants404ResponseFromJSON(
  json: any,
): ListAssistants404Response {
  return ListAssistants404ResponseFromJSONTyped(json, false);
}

export function ListAssistants404ResponseFromJSONTyped(
  json: any,
  ignoreDiscriminator: boolean,
): ListAssistants404Response {
  if (json == null) {
    return json;
  }
  return {
    message: json["message"] == null ? undefined : json["message"],
  };
}

export function ListAssistants404ResponseToJSON(
  json: any,
): ListAssistants404Response {
  return ListAssistants404ResponseToJSONTyped(json, false);
}

export function ListAssistants404ResponseToJSONTyped(
  value?: ListAssistants404Response | null,
  ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    message: value["message"],
  };
}
