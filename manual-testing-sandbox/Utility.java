public class Utility {
    // 这是一个示例实用程序类
    /**
     * 返回两个数字中的最大值。
     * @param a 第一个数字
     * @param b 第二个数字
     * @return a 和 b 的最大值
     */
    public static int max(int a, int b) {
        return a > b ? a : b;
    }

    /**
     * 返回两个数字中的最小值。
     * @param a 第一个数字
     * @param b 第二个数字
     * @return a 和 b 的最小值
     */
    public static int min(int a, int b) {
        return a < b ? a : b;
    }

    // 写一个加法方法
    







    
}