// Generated by continue

import { extractMinimalStackTraceInfo } from "./extractMinimalStackTraceInfo";

describe("extractMinimalStackTraceInfo", () => {
  it("should return an empty string when stack is not a string or an empty string", () => {
    expect(extractMinimalStackTraceInfo(undefined)).toBe("");
    expect(extractMinimalStackTraceInfo(null)).toBe("");
    expect(extractMinimalStackTraceInfo(1234)).toBe("");
    expect(extractMinimalStackTraceInfo({})).toBe("");
    expect(extractMinimalStackTraceInfo([])).toBe("");
    expect(extractMinimalStackTraceInfo(true)).toBe("");
    expect(extractMinimalStackTraceInfo("")).toBe("");
  });

  it("should extract minimal stack trace info from a valid stack trace", () => {
    const stack = `Error: Something went wrong
    at FunctionOne (/Users/<USER>/project/file1.ts:10:15)
    at FunctionTwo (/Users/<USER>/project/file2.ts:20:25)
    at FunctionThree (/Users/<USER>/project/file3.ts:30:35)
    at node_modules/some_module/index.js:40:45
    at FunctionFour (/Users/<USER>/project/file4.ts:50:55)
    at processTicksAndRejections (node:internal/process/task_queues:96:5)
    `;
    const expected = "FunctionOne, FunctionTwo, FunctionThree, FunctionFour";
    expect(extractMinimalStackTraceInfo(stack)).toBe(expected);
  });

  it("should return an empty string if no matching lines are found", () => {
    const stack = `Error: Something went wrong
    at node_modules/some_module/index.js:40:45
    at processTicksAndRejections (node:internal/process/task_queues:96:5)
    `;
    expect(extractMinimalStackTraceInfo(stack)).toBe("");
  });

  it("should handle stack traces without 'at ' at the beginning of lines", () => {
    const stack = `Error: Something went wrong
    in FunctionOne (/Users/<USER>/project/file1.ts:10:15)
    in FunctionTwo (/Users/<USER>/project/file2.ts:20:25)
    `;
    expect(extractMinimalStackTraceInfo(stack)).toBe("");
  });

  it("should handle stack traces with varying whitespace", () => {
    const stack = `Error: Something went wrong
at    FunctionOne    (/Users/<USER>/project/file1.ts:10:15)
    at FunctionTwo(/Users/<USER>/project/file2.ts:20:25)
  at   FunctionThree (/Users/<USER>/project/file3.ts:30:35)
  `;
    const expected = "FunctionOne, FunctionTwo(), FunctionThree";
    expect(extractMinimalStackTraceInfo(stack)).toBe(expected);
  });

  it("should correctly handle function names with spaces and special characters", () => {
    const stack = `Error: Something went wrong
    at Object.Function One (/Users/<USER>/project/file1.ts:10:15)
    at Module.<anonymous> (/Users/<USER>/project/file2.ts:20:25)
    at Function_Two (/Users/<USER>/project/file3.ts:30:35)
    `;
    const expected = "Object.Function One, Module.<anonymous>, Function_Two";
    expect(extractMinimalStackTraceInfo(stack)).toBe(expected);
  });

  it("should handle stack traces with anonymous functions", () => {
    const stack = `Error: Something went wrong
    at /Users/<USER>/project/file1.ts:10:15
    at /Users/<USER>/project/file2.ts:20:25
    at node_modules/some_module/index.js:40:45
    `;
    expect(extractMinimalStackTraceInfo(stack)).toBe("");
  });

  it("should handle lines without parentheses", () => {
    const stack = `Error: Something went wrong
    at FunctionOne
    at FunctionTwo
    at FunctionThree (/Users/<USER>/project/file3.ts:30:35)
    `;
    const expected = "FunctionOne, FunctionTwo, FunctionThree";
    expect(extractMinimalStackTraceInfo(stack)).toBe(expected);
  });
});
