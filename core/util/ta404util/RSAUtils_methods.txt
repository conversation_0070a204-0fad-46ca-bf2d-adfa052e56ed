Package Path: com.yinhai.tools.deps.crypto

public static void getKeyPairAndSave(String path);

public static void getKeyPairAndSave(String pubPath, String priPath);

public static String publicKey2Pem(PublicKey publicKey);

public static String privateKey2Pem(PrivateKey privateKey);

public static PublicKey string2PublicKey(String pubStr);

public static PrivateKey string2PrivateKey(String priStr);

public static PublicKey pem2PublicKey(String pubPemPath);

public static PrivateKey pem2PrivateKey(String priPemPath);

public static String encrypt(String content, PublicKey publicKey);

public static String decrypt(String content, PrivateKey privateKey);

public static String encryptWithPcks1(String content, PublicKey publicKey);

public static String decryptWithPcks1(String content, PrivateKey privateKey);

public static String sign(String content, PrivateKey privateKey);

public static boolean verify(String content, String sign, PublicKey publicKey);

public static KeyPair getKeyPair();

public static String getPublicKey(KeyPair keyPair);

