import os
import re
import json
import logging
import asyncio
from typing import List, Dict, Tuple, Optional
from pathlib import Path
from openai import AsyncOpenA<PERSON>
from dataclasses import dataclass

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class CodeChunk:
    file_path: str
    start_line: int
    chunk: str


@dataclass
class ScoredChunk:
    file: str
    start_line: int
    score: float
    code: str


class CodeSnippetAnalyzer:
    def __init__(self, api_key: str, model: str = "gpt-4o", max_chunk_size: int = 2000):
        self.client = AsyncOpenAI(api_key=api_key)
        self.model = model
        self.max_chunk_size = max_chunk_size
        self.system_prompt = """
        You are a senior code analysis expert specializing in Java and Spring framework projects. Your task is to evaluate code snippets for relevance to a user requirement. Follow these steps:

        1. Analyze the user requirement: "{user_request}"
        2. Break down the requirement into key components (e.g., functionality, variables, configuration).
        3. Evaluate each code snippet based on the following criteria (0-10 score, 10=most relevant):
           - Presence of relevant functionality (e.g., methods or classes implementing the requested feature).
           - Presence of relevant configuration or constants (e.g., cache prefix settings).
           - Relevant keywords or variable names matching the requirement.
           - Comments or documentation related to the requirement.
           - Penalize snippets that are unrelated or contain only generic code (e.g., imports, boilerplate).
        4. Consider the project context: This is a Java-based Spring framework project focused on cache management.
        5. Return JSON: {{"scores": [{{"file": "path", "start_line": number, "score": number}}]}}
        Return only JSON, no additional explanation.
        """

    async def read_file_chunks(self, file_path: str) -> List[CodeChunk]:
        try:
            with Path(file_path).open('r', encoding='utf-8') as f:
                content = f.read()
                if not content.strip():
                    logger.warning(f"File {file_path} is empty")
                    return []
                lines = content.splitlines(keepends=True)
        except (IOError, UnicodeDecodeError) as e:
            logger.error(f"Failed to read file {file_path}: {e}")
            return []

        chunks = []
        current_chunk = []
        current_line = 0
        start_line = 1
        brace_count = 0
        in_block = False

        for i, line in enumerate(lines):
            current_line = i + 1
            brace_count += line.count('{') - line.count('}')

            if brace_count > 0 and not in_block:
                in_block = True
            elif brace_count == 0 and in_block:
                in_block = False

            current_chunk.append(line)
            chunk_text = ''.join(current_chunk)

            if len(chunk_text) >= self.max_chunk_size and not in_block:
                chunks.append(CodeChunk(file_path, start_line, chunk_text))
                start_line = current_line + 1
                current_chunk = []
                brace_count = 0

        if current_chunk:
            chunks.append(CodeChunk(file_path, start_line, ''.join(current_chunk)))

        return chunks

    async def evaluate_relevance(self, user_request: str, code_chunks: List[CodeChunk]) -> List[Dict]:
        if not code_chunks:
            return []

        # Pre-filter chunks with keyword matching
        keywords = re.findall(r'\w+', user_request.lower())
        filtered_chunks = [
                              c for c in code_chunks
                              if any(keyword in c.chunk.lower() for keyword in keywords)
                          ] or code_chunks  # Fallback to all chunks if none match

        chunk_descriptions = [
            f"【Code Chunk {i + 1}】File: {c.file_path}\nStart Line: {c.start_line}\nContent:\n```java\n{c.chunk[:1500]}...\n```"
            for i, c in enumerate(filtered_chunks)
        ]

        user_content = f"Requirement Analysis:\n{user_request}\n\nCode Snippets:\n" + "\n\n".join(chunk_descriptions)

        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": self.system_prompt.format(user_request=user_request)},
                    {"role": "user", "content": user_content}
                ],
                temperature=0.0,  # Lower temperature for consistency
                max_tokens=2000,
                response_format={"type": "json_object"}
            )

            result = json.loads(response.choices[0].message.content)
            # Normalize scores
            scores = result["scores"]
            if scores:
                max_score = max(s["score"] for s in scores)
                if max_score > 0:
                    for s in scores:
                        s["score"] = (s["score"] / max_score) * 10  # Normalize to 0-10
            return scores

        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
            return [{"file": c.file_path, "start_line": c.start_line, "score": 0} for c in filtered_chunks]

    async def get_relevant_snippets(self,
                                    base_dir: str,
                                    modules: List[str],
                                    files: List[str],
                                    user_request: str,
                                    top_n: int = 5,
                                    batch_size: int = 10) -> List[ScoredChunk]:
        if not all([base_dir, modules, files, user_request]):
            raise ValueError("Base directory, modules, files, and user request must be provided and non-empty")

        base_path = Path(base_dir)
        if not base_path.exists():
            raise ValueError(f"Base directory {base_dir} does not exist")

        # Collect chunks concurrently
        all_chunks = []
        tasks = []
        for module in modules:
            module_path = base_path / module
            for file in files:
                file_path = module_path / file
                if file_path.exists():
                    tasks.append(self.read_file_chunks(str(file_path)))
                else:
                    logger.warning(f"File {file_path} does not exist, skipping")

        chunk_lists = await asyncio.gather(*tasks, return_exceptions=True)
        for chunks in chunk_lists:
            if isinstance(chunks, list):
                all_chunks.extend(chunks)
            else:
                logger.error(f"Error reading file: {chunks}")

        if not all_chunks:
            logger.warning("No valid code chunks found")
            return []

        # Process chunks in batches
        scored_chunks = []
        batch_tasks = [
            self.evaluate_relevance(user_request, all_chunks[i:i + batch_size])
            for i in range(0, len(all_chunks), batch_size)
        ]
        batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
        for result in batch_results:
            if isinstance(result, list):
                scored_chunks.extend(result)
            else:
                logger.error(f"Error evaluating batch: {result}")

        # Sort and select top N
        sorted_chunks = sorted(scored_chunks, key=lambda x: x["score"], reverse=True)[:top_n]

        results = []
        for chunk in sorted_chunks:
            for orig_chunk in all_chunks:
                if (orig_chunk.file_path == chunk["file"] and
                        orig_chunk.start_line == chunk["start_line"]):
                    results.append(ScoredChunk(
                        file=chunk["file"],
                        start_line=chunk["start_line"],
                        score=chunk["score"],
                        code=orig_chunk.chunk
                    ))
                    break

        return results


async def main():
    analyzer = CodeSnippetAnalyzer(
        api_key="********************************************************************************************************************************************************************",
        model="gpt-4o",
        max_chunk_size=2000
    )

    try:
        snippets = await analyzer.get_relevant_snippets(
            base_dir="/Users/<USER>/IdeaProjects/ta3404-back-components",
            modules=["ta404-project/ta404-modules/ta404-module-cache/ta404-module-cache-starter-redis"],
            files=['src/main/java/com/yinhai/ta404/module/cache/starter/redis/TaRedisCacheManager.java',
                    'src/main/java/com/yinhai/ta404/module/cache/starter/redis/autoconfigure/TaRedisProperties.java',
                    'src/main/java/com/yinhai/ta404/module/cache/starter/redis/autoconfigure/CacheConfig.java'],
            user_request="Modify framework cache default prefix",
            top_n=3
        )

        logger.info(f"Found {len(snippets)} relevant snippets:")
        for i, snippet in enumerate(snippets):
            logger.info(
                f"Snippet #{i + 1} - File: {snippet.file} "
                f"(Line: {snippet.start_line}, Score: {snippet.score}/10)"
            )
            logger.info(f"Code:\n{snippet.code}...")
            logger.info("----------------------------------")
            logger.info("----------------------------------")
            logger.info("----------------------------------")

    except Exception as e:
        logger.error(f"Error in main execution: {e}")


if __name__ == "__main__":
    asyncio.run(main())