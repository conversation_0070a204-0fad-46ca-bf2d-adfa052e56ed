// @ts-nocheck

class Group {
  getPersonAddress(person: Person): Address {
    // TODO
  }

  getHardcodedAddress(): Address {
    // TODO
  }

  addPerson(person: Person) {
    // TODO
  }

  addPeople(people: Person[]) {
    // TODO
  }

  getAddresses(people: Person[]): Address[] {
    // TODO
  }

  logPersonWithAddress(person: Person<Address>): Person<Address> {
    // TODO
  }

  logPersonOrAddress(person: Person | Address): Person | Address {
    // TODO
  }

  logPersonAndAddress(person: Person, address: Address) {
    // TODO
  }
}
